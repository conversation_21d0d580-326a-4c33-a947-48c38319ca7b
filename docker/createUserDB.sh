#!/bin/bash

set -e

psql --username postgres --dbname postgres -tAc "SELECT 1 FROM pg_roles WHERE rolname='core'" | \
grep -q 1 || \
psql -v ON_ERROR_STOP=1 --username postgres --dbname postgres <<-EOSQL
      CREATE USER core WITH ENCRYPTED PASSWORD 'Good2No!';
      ALTER USER core CREATEDB;
EOSQL

psql -v ON_ERROR_STOP=1 --username postgres --dbname postgres <<-EOSQL
      DROP DATABASE IF EXISTS core WITH (FORCE);
EOSQL

psql -v ON_ERROR_STOP=1 --username postgres --dbname postgres <<-EOSQL
	    CREATE DATABASE core;
	    ALTER DATABASE core OWNER TO core;
	    GRANT ALL PRIVILEGES ON DATABASE core TO core;
EOSQL
