import { ComponentFixture, TestBed } from '@angular/core/testing';
import { Router, NavigationEnd } from '@angular/router';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { of, Subject } from 'rxjs';

import { DorseyCoreComponent } from './dorsey-core.component';
import { EditingStateService } from './core/services/editing-state.service';
import { LoadingService } from './core/services/loading.service';
import { ToastService } from './core/services/toast.service';
import { WebSocketAPI } from './core/services/notifications/web-socket-api';
import { AccountService } from './core/auth/account.service';
import { SystemService } from './core/services/admin/system.service';
import { HeaderService } from './core/services/admin/header.service';
import { UserDataService } from './core/services/admin/user-data.service';
import { DorseyConfiguration } from './core/models/configuration';
import { MenuItem } from './core/models/menu-item';

describe('DorseyCoreComponent', () => {
  let component: DorseyCoreComponent;
  let fixture: ComponentFixture<DorseyCoreComponent>;
  let mockEditingStateService: jest.Mocked<EditingStateService>;
  let mockRouter: jest.Mocked<Router>;
  let mockLoadingService: jest.Mocked<LoadingService>;
  let mockWebSocketAPI: jest.Mocked<WebSocketAPI>;
  let mockToastService: jest.Mocked<ToastService>;
  let mockAccountService: jest.Mocked<AccountService>;
  let mockSystemService: jest.Mocked<SystemService>;
  let mockHeaderService: jest.Mocked<HeaderService>;
  let mockUserDataService: jest.Mocked<UserDataService>;
  let mockConfig: DorseyConfiguration;

  const mockUser = { id: '1', name: 'Test User', email: '<EMAIL>' };
  const mockSystemConfig = {
    theme: 2,
    colors: ['#3A5976', '#0086ad', '#A9C3C5', '#DDDDDD']
  };
  const mockMenuItems: MenuItem[] = [
    { label: 'Home', icon: 'fa-home', routerLink: '/home', items: [] }
  ];

  beforeEach(async () => {
    const routerEventsSubject = new Subject();

    mockEditingStateService = {
      setPrevUrl: jest.fn(),
      setCurrUrl: jest.fn(),
      getCurrUrl: jest.fn().mockReturnValue('/current'),
      getEditingState: jest.fn().mockReturnValue(false),
      getPrevUrl: jest.fn().mockReturnValue('/previous')
    } as any;

    mockRouter = {
      events: routerEventsSubject.asObservable()
    } as any;

    mockLoadingService = {
      isLoading: false
    } as any;

    mockWebSocketAPI = {
      connect: jest.fn()
    } as any;

    mockToastService = {} as any;

    mockAccountService = {
      identity: jest.fn().mockReturnValue(of(mockUser))
    } as any;

    mockSystemService = {
      findConfig: jest.fn().mockReturnValue(of(mockSystemConfig))
    } as any;

    mockHeaderService = {
      skin: 1
    } as any;

    mockUserDataService = {
      setUser: jest.fn()
    } as any;

    mockConfig = {
      theme: {
        number: 1,
        colors: ['#default1', '#default2', '#default3', '#default4']
      },
      environment: {
        apiUrl: 'http://localhost:8080/api'
      }
    } as any;

    // Mock document.documentElement.style.setProperty
    Object.defineProperty(document.documentElement.style, 'setProperty', {
      value: jest.fn(),
      writable: true
    });

    await TestBed.configureTestingModule({
      declarations: [DorseyCoreComponent],
      providers: [
        { provide: EditingStateService, useValue: mockEditingStateService },
        { provide: Router, useValue: mockRouter },
        { provide: LoadingService, useValue: mockLoadingService },
        { provide: WebSocketAPI, useValue: mockWebSocketAPI },
        { provide: ToastService, useValue: mockToastService },
        { provide: AccountService, useValue: mockAccountService },
        { provide: SystemService, useValue: mockSystemService },
        { provide: HeaderService, useValue: mockHeaderService },
        { provide: UserDataService, useValue: mockUserDataService },
        { provide: 'config', useValue: mockConfig }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();

    fixture = TestBed.createComponent(DorseyCoreComponent);
    component = fixture.componentInstance;
  });

  describe('Component Initialization', () => {
    it('should create', () => {
      expect(component).toBeTruthy();
    });

    it('should initialize with default values', () => {
      expect(component.display).toBe(false);
      expect(component.initialRoute).toBeUndefined();
    });

    it('should accept menuItems input', () => {
      component.menuItems = mockMenuItems;
      expect(component.menuItems).toBe(mockMenuItems);
    });
  });

  describe('Constructor and User Authentication', () => {
    it('should call identity service and set up user', () => {
      expect(mockAccountService.identity).toHaveBeenCalled();
      expect(mockSystemService.findConfig).toHaveBeenCalled();
      expect(mockUserDataService.setUser).toHaveBeenCalledWith(mockUser);
      expect(mockWebSocketAPI.connect).toHaveBeenCalled();
    });

    it('should set CSS custom properties from system config', () => {
      expect(document.documentElement.style.setProperty).toHaveBeenCalledWith(
        '--primary-color', mockSystemConfig.colors[0]
      );
      expect(document.documentElement.style.setProperty).toHaveBeenCalledWith(
        '--secondary-color', mockSystemConfig.colors[1]
      );
      expect(document.documentElement.style.setProperty).toHaveBeenCalledWith(
        '--tertiary-color', mockSystemConfig.colors[2]
      );
      expect(document.documentElement.style.setProperty).toHaveBeenCalledWith(
        '--quaternary-color', mockSystemConfig.colors[3]
      );
    });

    it('should set header service skin from system config', () => {
      expect(mockHeaderService.skin).toBe(mockSystemConfig.theme);
    });

    it('should set display to true after config load', () => {
      expect(component.display).toBe(true);
    });

    it('should handle system config without colors', () => {
      const configWithoutColors = { theme: 1, colors: [] };
      mockSystemService.findConfig.mockReturnValue(of(configWithoutColors));

      // Re-create component to trigger constructor
      fixture = TestBed.createComponent(DorseyCoreComponent);
      component = fixture.componentInstance;

      expect(mockHeaderService.skin).toBe(1);
    });

    it('should handle missing system config', () => {
      const emptyConfig = {};
      mockSystemService.findConfig.mockReturnValue(of(emptyConfig));

      // Re-create component to trigger constructor
      fixture = TestBed.createComponent(DorseyCoreComponent);
      component = fixture.componentInstance;

      expect(mockHeaderService.skin).toBe(mockConfig.theme.number);
    });
  });

  describe('Router Events Handling', () => {
    it('should handle navigation end events', () => {
      const mockNavigationEnd = new NavigationEnd(1, '/test', '/test');
      const routerEventsSubject = new Subject();
      mockRouter.events = routerEventsSubject.asObservable();

      // Re-create component to trigger constructor subscription
      fixture = TestBed.createComponent(DorseyCoreComponent);
      component = fixture.componentInstance;
      component.themeComponent = {
        currentTheme: {
          header: { loadBreadcrumb: jest.fn() },
          menu: { onPathChange: jest.fn() }
        }
      } as any;

      routerEventsSubject.next(mockNavigationEnd);

      expect(component.initialRoute).toEqual(JSON.parse(JSON.stringify(mockNavigationEnd)));
      expect(mockEditingStateService.setPrevUrl).toHaveBeenCalledWith('/current');
      expect(mockEditingStateService.setCurrUrl).toHaveBeenCalledWith(location.pathname);
    });

    it('should handle navigation events without theme component', () => {
      const mockNavigationEnd = new NavigationEnd(1, '/test', '/test');
      const routerEventsSubject = new Subject();
      mockRouter.events = routerEventsSubject.asObservable();

      // Re-create component to trigger constructor subscription
      fixture = TestBed.createComponent(DorseyCoreComponent);
      component = fixture.componentInstance;

      expect(() => {
        routerEventsSubject.next(mockNavigationEnd);
      }).not.toThrow();
    });
  });

  describe('ngAfterViewChecked', () => {
    it('should load breadcrumb when display is true and initialRoute exists', () => {
      component.display = true;
      component.initialRoute = { url: '/test' };
      component.themeComponent = {
        currentTheme: {
          header: { loadBreadcrumb: jest.fn() }
        }
      } as any;

      component.ngAfterViewChecked();

      expect(component.themeComponent.currentTheme.header.loadBreadcrumb).toHaveBeenCalledWith({ url: '/test' });
      expect(component.initialRoute).toBeNull();
    });

    it('should not load breadcrumb when display is false', () => {
      component.display = false;
      component.initialRoute = { url: '/test' };
      component.themeComponent = {
        currentTheme: {
          header: { loadBreadcrumb: jest.fn() }
        }
      } as any;

      component.ngAfterViewChecked();

      expect(component.themeComponent.currentTheme.header.loadBreadcrumb).not.toHaveBeenCalled();
      expect(component.initialRoute).toEqual({ url: '/test' });
    });

    it('should not load breadcrumb when initialRoute is null', () => {
      component.display = true;
      component.initialRoute = null;
      component.themeComponent = {
        currentTheme: {
          header: { loadBreadcrumb: jest.fn() }
        }
      } as any;

      component.ngAfterViewChecked();

      expect(component.themeComponent.currentTheme.header.loadBreadcrumb).not.toHaveBeenCalled();
    });

    it('should handle missing theme component gracefully', () => {
      component.display = true;
      component.initialRoute = { url: '/test' };
      component.themeComponent = undefined;

      expect(() => {
        component.ngAfterViewChecked();
      }).not.toThrow();
    });
  });

  describe('Host Listeners', () => {
    it('should handle beforeunload event when not editing', () => {
      mockEditingStateService.getEditingState.mockReturnValue(false);

      const result = component.beforeUnloadHandler();

      expect(result).toBe(true);
      expect(mockEditingStateService.getEditingState).toHaveBeenCalled();
    });

    it('should handle beforeunload event when editing', () => {
      mockEditingStateService.getEditingState.mockReturnValue(true);

      const result = component.beforeUnloadHandler();

      expect(result).toBe(false);
    });

    it('should handle popstate event when editing', () => {
      mockEditingStateService.getEditingState.mockReturnValue(true);
      const pushStateSpy = jest.spyOn(window.history, 'pushState').mockImplementation(() => {});

      component.hashChangeHandler();

      expect(pushStateSpy).toHaveBeenCalledWith('', '', '/previous');

      pushStateSpy.mockRestore();
    });

    it('should not handle popstate event when not editing', () => {
      mockEditingStateService.getEditingState.mockReturnValue(false);
      const pushStateSpy = jest.spyOn(window.history, 'pushState').mockImplementation(() => {});

      component.hashChangeHandler();

      expect(pushStateSpy).not.toHaveBeenCalled();

      pushStateSpy.mockRestore();
    });
  });

  describe('Service Integration', () => {
    it('should inject all required services', () => {
      expect(component.loadingService).toBe(mockLoadingService);
      expect(component.headerService).toBe(mockHeaderService);
    });

    it('should handle loading service state', () => {
      mockLoadingService.isLoading = true;
      expect(component.loadingService.isLoading).toBe(true);

      mockLoadingService.isLoading = false;
      expect(component.loadingService.isLoading).toBe(false);
    });
  });

  describe('ViewChild References', () => {
    it('should have themeComponent ViewChild reference', () => {
      expect(component.themeComponent).toBeUndefined();
    });
  });

  describe('Template Integration', () => {
    it('should render without errors', () => {
      component.menuItems = mockMenuItems;

      expect(() => {
        fixture.detectChanges();
      }).not.toThrow();
    });

    it('should maintain component state after template rendering', () => {
      component.display = true;
      component.menuItems = mockMenuItems;

      fixture.detectChanges();

      expect(component.display).toBe(true);
      expect(component.menuItems).toBe(mockMenuItems);
    });
  });

  describe('Edge Cases', () => {
    it('should handle empty menuItems array', () => {
      component.menuItems = [];

      expect(() => {
        fixture.detectChanges();
      }).not.toThrow();
    });

    it('should handle undefined menuItems', () => {
      component.menuItems = undefined;

      expect(() => {
        fixture.detectChanges();
      }).not.toThrow();
    });

    it('should handle user authentication failure', () => {
      mockAccountService.identity.mockReturnValue(of(null));

      // Re-create component to trigger constructor
      expect(() => {
        fixture = TestBed.createComponent(DorseyCoreComponent);
        component = fixture.componentInstance;
      }).not.toThrow();
    });
  });
});
