import { ComponentFixture, TestBed } from '@angular/core/testing';
import { HttpClient } from '@angular/common/http';
import { Router } from '@angular/router';
import { ActivatedRoute } from '@angular/router';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { of, throwError } from 'rxjs';

import { FileBrowserComponent } from './file-browser.component';
import { DorseyConfiguration } from '../../../models/configuration';

// Mock jQuery
declare var $: any;
declare var global: any;
(global as any).$ = jest.fn(() => ({
  append: jest.fn(),
  select: jest.fn(),
  remove: jest.fn(),
  val: jest.fn(),
  toast: jest.fn()
}));

// Mock document methods
Object.defineProperty(document, 'execCommand', {
  value: jest.fn(),
  writable: true
});

Object.defineProperty(document, 'createElement', {
  value: jest.fn(() => ({
    href: '',
    download: '',
    click: jest.fn(),
    appendChild: jest.fn(),
    removeChild: jest.fn()
  })),
  writable: true
});

Object.defineProperty(document, 'getElementById', {
  value: jest.fn(() => ({
    files: [{ name: 'test.txt', size: 1024 }]
  })),
  writable: true
});

// Mock window methods
Object.defineProperty(window, 'URL', {
  value: {
    createObjectURL: jest.fn(() => 'mock-url')
  },
  writable: true
});

Object.defineProperty(window, 'open', {
  value: jest.fn(),
  writable: true
});

Object.defineProperty(window, 'close', {
  value: jest.fn(),
  writable: true
});

Object.defineProperty(window, 'opener', {
  value: {
    postMessage: jest.fn()
  },
  writable: true
});

describe('FileBrowserComponent', () => {
  let component: FileBrowserComponent;
  let fixture: ComponentFixture<FileBrowserComponent>;
  let mockHttpClient: jest.Mocked<HttpClient>;
  let mockRouter: jest.Mocked<Router>;
  let mockActivatedRoute: jest.Mocked<ActivatedRoute>;
  let mockConfig: DorseyConfiguration;

  const mockDirectories = [
    { name: 'folder1', path: '/root/folder1', pathSeparator: '/' },
    { name: 'folder2', path: '/root/folder2', pathSeparator: '/' }
  ];

  const mockFiles = [
    { name: 'document.pdf', path: '/root/document.pdf', size: 1024 },
    { name: 'image.jpg', path: '/root/image.jpg', size: 2048 },
    { name: 'text.txt', path: '/root/text.txt', size: 512 }
  ];

  beforeEach(async () => {
    mockHttpClient = {
      get: jest.fn(),
      post: jest.fn()
    } as any;

    mockRouter = {
      navigate: jest.fn().mockResolvedValue(true)
    } as any;

    mockActivatedRoute = {
      snapshot: {
        queryParamMap: {
          get: jest.fn((key: string) => {
            if (key === 'CKEditorFuncNum') return null;
            if (key === 'appId') return 'test-app';
            return null;
          })
        }
      }
    } as any;

    mockConfig = {
      environment: {
        apiUrl: 'http://localhost:8080/api'
      }
    } as any;

    // Mock sessionStorage
    Object.defineProperty(window, 'sessionStorage', {
      value: {
        getItem: jest.fn(() => null),
        setItem: jest.fn(),
        removeItem: jest.fn()
      },
      writable: true
    });

    await TestBed.configureTestingModule({
      declarations: [FileBrowserComponent],
      providers: [
        { provide: HttpClient, useValue: mockHttpClient },
        { provide: Router, useValue: mockRouter },
        { provide: ActivatedRoute, useValue: mockActivatedRoute },
        { provide: 'config', useValue: mockConfig }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();

    fixture = TestBed.createComponent(FileBrowserComponent);
    component = fixture.componentInstance;
  });

  describe('Component Initialization', () => {
    it('should create', () => {
      expect(component).toBeTruthy();
    });

    it('should initialize with default values', () => {
      expect(component.dirs).toEqual([]);
      expect(component.files).toEqual([]);
      expect(component.filesCopy).toEqual([]);
      expect(component.breadcrumbs).toEqual([]);
      expect(component.selectedDir).toEqual({});
      expect(component.alertMessage).toBe('');
      expect(component.alertSuccess).toBe(true);
      expect(component.dirMap).toEqual({});
      expect(component.filter).toBe('');
      expect(component.loading).toBe(false);
      expect(component.searchOptions).toEqual([]);
    });

    it('should extract query parameters from route', () => {
      expect(component.ckEditorFuncNum).toBeNull();
      expect(component.appId).toBe('test-app');
    });
  });

  describe('ngOnInit', () => {
    it('should initialize without CKEditor and call setContentRoot', () => {
      jest.spyOn(component, 'setContentRoot').mockImplementation(() => {});

      component.ngOnInit();

      expect(component.loggedInUser).toBeNull();
      expect(component.loading).toBe(false);
      expect(component.setContentRoot).toHaveBeenCalled();
    });

    it('should navigate to login when CKEditor is present but no logged in user', () => {
      component.ckEditorFuncNum = '123';
      (window.sessionStorage.getItem as jest.Mock).mockReturnValue(null);

      component.ngOnInit();

      expect(mockRouter.navigate).toHaveBeenCalledWith(['login'], {
        queryParams: { logout: 'false' }
      });
    });

    it('should set logged in user and call setContentRoot when CKEditor is present', () => {
      component.ckEditorFuncNum = '123';
      const mockUser = { id: '1', name: 'Test User' };
      (window.sessionStorage.getItem as jest.Mock).mockReturnValue(JSON.stringify(mockUser));
      jest.spyOn(component, 'setContentRoot').mockImplementation(() => {});

      component.ngOnInit();

      expect(component.loggedInUser).toEqual(mockUser);
      expect(component.setContentRoot).toHaveBeenCalled();
    });
  });

  describe('Content Root and File Loading', () => {
    beforeEach(() => {
      jest.spyOn(component, 'listFilesAndDirectories').mockImplementation(() => {});
    });

    it('should set content root successfully', () => {
      mockHttpClient.get.mockReturnValue(of('/root/documents'));

      component.setContentRoot();

      expect(component.loading).toBe(true);
      expect(mockHttpClient.get).toHaveBeenCalledWith(
        'http://localhost:8080/api/library/getFileRoot',
        { responseType: 'text' }
      );
      expect(component.docbase).toBe('/root/documents');
      expect(component.listFilesAndDirectories).toHaveBeenCalled();
    });

    it('should list files and directories successfully', () => {
      component.docbase = '/root';
      component.contentRoot = '/root';
      mockHttpClient.get.mockReturnValueOnce(of(mockDirectories));
      mockHttpClient.get.mockReturnValueOnce(of(mockFiles));
      jest.spyOn(component, 'setDirMap').mockImplementation(() => {});
      jest.spyOn(component, 'setBreadCrumbState').mockImplementation(() => {});

      component.listFilesAndDirectories();

      expect(component.loading).toBe(false);
      expect(component.dirs).toEqual(mockDirectories);
      expect(component.breadcrumbs.length).toBe(1);
      expect(component.selectedDir).toBe(component.breadcrumbs[0]);
      expect(component.setDirMap).toHaveBeenCalled();
      expect(component.setBreadCrumbState).toHaveBeenCalled();
    });

    it('should handle empty directory response', () => {
      mockHttpClient.get.mockReturnValue(of([]));

      component.listFilesAndDirectories();

      expect(component.loading).toBe(false);
      expect(component.dirs).toEqual([]);
    });

    it('should get files metadata', () => {
      const mockMetadata = [{ name: 'file1.txt', size: 1024, modified: '2023-01-01' }];
      mockHttpClient.get.mockReturnValue(of(mockMetadata));

      component.getFilesMetaData('/test/dir').subscribe(result => {
        expect(result).toEqual(mockMetadata);
      });

      expect(mockHttpClient.get).toHaveBeenCalledWith(
        'http://localhost:8080/api/library/getFilesMetaData?dir=/test/dir'
      );
    });
  });

  describe('Directory Navigation', () => {
    beforeEach(() => {
      component.docbase = '/root';
      component.contentRoot = '/root';
      jest.spyOn(component, 'setDirMap').mockImplementation(() => {});
      jest.spyOn(component as any, 'resetBreadcrumbs').mockImplementation(() => {});
      jest.spyOn(component as any, 'loadSearchOptions').mockImplementation(() => {});
      jest.spyOn(component, 'determineUrlPath').mockReturnValue('encoded/path');
    });

    it('should handle directory click successfully', () => {
      const mockDir = { name: 'folder1', path: '/root/folder1', pathSeparator: '/' };
      mockHttpClient.get.mockReturnValueOnce(of(mockDirectories));
      mockHttpClient.get.mockReturnValueOnce(of(mockFiles));

      component.onDirClick(mockDir);

      expect(component.loading).toBe(true);
      expect(component.setDirMap).toHaveBeenCalledWith(mockDir);
      expect(component['resetBreadcrumbs']).toHaveBeenCalledWith(mockDir);
      expect(component.selectedDir).toBe(mockDir);
      expect(component['loadSearchOptions']).toHaveBeenCalledWith('encoded/path');
    });

    it('should handle directory click with event prevention', () => {
      const mockEvent = { preventDefault: jest.fn() };
      const mockDir = { name: 'folder1', path: '/root/folder1', pathSeparator: '/' };
      mockHttpClient.get.mockReturnValue(of([]));

      component.onDirClick(mockDir, mockEvent);

      expect(mockEvent.preventDefault).toHaveBeenCalled();
    });

    it('should handle empty directory object', () => {
      mockHttpClient.get.mockReturnValue(of([]));

      component.onDirClick({});

      expect(component.determineUrlPath).toHaveBeenCalledWith({
        path: '',
        name: '',
        pathSeparator: '/'
      });
    });

    it('should load search options correctly', () => {
      const mockSearchData = [
        { name: 'file1.txt', flag: 'file' },
        { name: 'folder1', flag: 'dir' }
      ];
      mockHttpClient.get.mockReturnValue(of(mockSearchData));

      component['loadSearchOptions']('/test/path');

      expect(mockHttpClient.get).toHaveBeenCalledWith(
        'http://localhost:8080/api/library/listAllFilesAndDirs?dir=/test/path'
      );
      expect(component.searchOptions).toEqual(mockSearchData);
      expect(component.searchValue).toBe('');
    });
  });

  describe('File Operations', () => {
    beforeEach(() => {
      component.selectedDir = { name: 'test', path: '/test', pathSeparator: '/' };
      jest.spyOn(component, 'determineUrlPath').mockReturnValue('test/path');
      jest.spyOn(component, 'processResponse').mockImplementation(() => {});
    });

    it('should upload file successfully', () => {
      const mockFormData = new FormData();
      mockHttpClient.post.mockReturnValue(of({ success: true }));

      component.uploadFile();

      expect(component.alertMessage).toBe('');
      expect(component.alertSuccess).toBe(true);
      expect(component.loading).toBe(true);
      expect(mockHttpClient.post).toHaveBeenCalledWith(
        'http://localhost:8080/api/library/uploadFile?dir=test/path',
        expect.any(FormData)
      );
      expect(component.processResponse).toHaveBeenCalledWith({ success: true });
    });

    it('should download file successfully', () => {
      const mockFile = { name: 'test.pdf', path: '/test/test.pdf' };
      const mockBlob = new Blob(['test content'], { type: 'application/pdf' });
      mockHttpClient.get.mockReturnValue(of(mockBlob));
      const mockEvent = { preventDefault: jest.fn() };

      component.onFileDownload(mockFile, mockEvent);

      expect(mockEvent.preventDefault).toHaveBeenCalled();
      expect(component.alertSuccess).toBe(true);
      expect(component.alertMessage).toBe('');
      expect(component.loading).toBe(true);
      expect(mockHttpClient.get).toHaveBeenCalledWith(
        'http://localhost:8080/api/library/getFile/test.pdf?filePath=test/path',
        { responseType: 'blob' }
      );
    });

    it('should handle file click for CKEditor', () => {
      component.ckEditorFuncNum = '123';
      const mockFile = { name: 'test.jpg' };
      const mockEvent = { preventDefault: jest.fn() };
      jest.spyOn(component, 'getFileUrl').mockReturnValue('http://example.com/test.jpg');

      component.onFileClick(mockFile, mockEvent);

      expect(mockEvent.preventDefault).toHaveBeenCalled();
      expect(window.opener.postMessage).toHaveBeenCalledWith({
        ckEditorFuncNum: '123',
        fileUrl: 'http://example.com/test.jpg'
      }, '*');
      expect(window.close).toHaveBeenCalled();
    });

    it('should handle file click for regular view', () => {
      component.ckEditorFuncNum = null;
      const mockFile = { name: 'test.jpg' };
      const mockEvent = { preventDefault: jest.fn() };
      jest.spyOn(component, 'getFileUrl').mockReturnValue('http://example.com/test.jpg');

      component.onFileClick(mockFile, mockEvent);

      expect(mockEvent.preventDefault).toHaveBeenCalled();
      expect(window.open).toHaveBeenCalledWith('http://example.com/test.jpg', '_blank');
    });
  });

  describe('File Validation', () => {
    it('should validate PDF files', () => {
      expect(component.validFile('document.pdf')).toBe(true);
      expect(component.validFile('DOCUMENT.PDF')).toBe(true);
    });

    it('should validate image files', () => {
      expect(component.validFile('image.jpg')).toBe(true);
      expect(component.validFile('photo.jpeg')).toBe(true);
      expect(component.validFile('picture.png')).toBe(true);
      expect(component.validFile('animation.gif')).toBe(true);
    });

    it('should reject invalid file types', () => {
      expect(component.validFile('document.doc')).toBe(false);
      expect(component.validFile('spreadsheet.xlsx')).toBe(false);
      expect(component.validFile('text.txt')).toBe(false);
      expect(component.validFile('video.mp4')).toBe(false);
    });

    it('should handle empty filename', () => {
      expect(component.validFile('')).toBe(false);
    });
  });

  describe('Search Functionality', () => {
    beforeEach(() => {
      component.searchOptions = [
        { name: 'document.pdf', flag: 'file' },
        { name: 'image.jpg', flag: 'file' },
        { name: 'folder1', flag: 'dir' },
        { name: 'test_file.txt', flag: 'file' }
      ];
    });

    it('should return empty array for empty search term', (done) => {
      component.search(of('')).subscribe(result => {
        expect(result).toEqual([]);
        done();
      });
    });

    it('should filter search options by term', (done) => {
      component.search(of('doc')).subscribe(result => {
        expect(result.length).toBe(1);
        expect(result[0].name).toBe('document.pdf');
        done();
      });
    });

    it('should limit search results to 10 items', (done) => {
      // Create more than 10 search options
      component.searchOptions = Array.from({ length: 15 }, (_, i) => ({
        name: `file${i}.txt`,
        flag: 'file'
      }));

      component.search(of('file')).subscribe(result => {
        expect(result.length).toBe(10);
        done();
      });
    });

    it('should format search results correctly', () => {
      const mockItem = { name: 'test.pdf', flag: 'file' };
      expect(component.formatter(mockItem)).toBe('test.pdf');
    });
  });

  describe('Utility Functions', () => {
    it('should determine URL path correctly', () => {
      const dir1 = { name: 'folder', path: '', pathSeparator: '/' };
      expect(component.determineUrlPath(dir1)).toBe('folder');

      const dir2 = { name: 'subfolder', path: 'parent', pathSeparator: '/' };
      expect(component.determineUrlPath(dir2)).toBe('parent%2Fsubfolder');

      const dir3 = { name: '', path: '', pathSeparator: '/' };
      expect(component.determineUrlPath(dir3)).toBe('');
    });

    it('should copy file path to clipboard', () => {
      const mockFile = { name: 'test.pdf' };
      const mockEvent = { preventDefault: jest.fn() };
      jest.spyOn(component, 'getFileUrl').mockReturnValue('http://example.com/test.pdf');

      component.copyPath(mockFile, mockEvent);

      expect(mockEvent.preventDefault).toHaveBeenCalled();
      expect(document.execCommand).toHaveBeenCalledWith('copy');
    });

    it('should refresh current directory', () => {
      const mockEvent = { preventDefault: jest.fn() };
      component.selectedDir = { name: 'current', path: '/current' };
      jest.spyOn(component, 'onDirClick').mockImplementation(() => {});

      component.refresh(mockEvent);

      expect(mockEvent.preventDefault).toHaveBeenCalled();
      expect(component.onDirClick).toHaveBeenCalledWith(component.selectedDir);
    });
  });

  describe('Error Handling', () => {
    it('should handle HTTP errors gracefully', () => {
      mockHttpClient.get.mockReturnValue(throwError('Network error'));

      expect(() => {
        component.setContentRoot();
      }).not.toThrow();
    });

    it('should handle file upload errors', () => {
      mockHttpClient.post.mockReturnValue(throwError('Upload failed'));

      expect(() => {
        component.uploadFile();
      }).not.toThrow();
    });

    it('should handle file download errors', () => {
      const mockFile = { name: 'test.pdf' };
      const mockEvent = { preventDefault: jest.fn() };
      mockHttpClient.get.mockReturnValue(throwError('Download failed'));

      expect(() => {
        component.onFileDownload(mockFile, mockEvent);
      }).not.toThrow();
    });
  });

  describe('Edge Cases', () => {
    it('should handle null or undefined directory objects', () => {
      mockHttpClient.get.mockReturnValue(of([]));

      expect(() => {
        component.onDirClick(null);
      }).not.toThrow();

      expect(() => {
        component.onDirClick(undefined);
      }).not.toThrow();
    });

    it('should handle missing file input element', () => {
      (document.getElementById as jest.Mock).mockReturnValue(null);

      expect(() => {
        component.uploadFile();
      }).not.toThrow();
    });

    it('should handle missing session storage', () => {
      (window.sessionStorage.getItem as jest.Mock).mockImplementation(() => {
        throw new Error('Storage not available');
      });

      expect(() => {
        component.ngOnInit();
      }).not.toThrow();
    });
  });
});
