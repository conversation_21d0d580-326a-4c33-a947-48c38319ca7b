<div class="d-flex flex-column p-3 h-100">
  <p-panel id="main-panel">
    <ng-template pTemplate="header">
      <div class="row col-12 m-0">
        <div class="row col-12 p-0 text-end apply-gap justify-content-between">
          <div class="col-9 d-flex">
            <div class="d-flex p-0 align-items-center me-5">
              <div class="d-flex text-end me-2">Completed</div>
              <div class="d-flex p-0">
                <p-inputSwitch class="d-flex" [(ngModel)]="completed"></p-inputSwitch>
              </div>
            </div>
            <div *ngIf="completed" class="d-flex p-0 align-items-center me-2">
              <div class="d-flex me-2">Created from</div>
              <div class="d-flex">
                <p-calendar
                  id="startDate"
                  placeholder="Select a date"
                  [showIcon]="true"
                  [dataType]="'string'"
                  [dateFormat]="'yy-mm-dd'"
                  [(ngModel)]="startDate"
                  appendTo="body"
                ></p-calendar>
              </div>
            </div>
            <div *ngIf="completed" class="d-flex p-0 align-items-center">
              <div class="d-flex me-2">to</div>
              <div class="d-flex">
                <p-calendar
                  id="endDate"
                  placeholder="Select a date"
                  [showIcon]="true"
                  [dataType]="'string'"
                  [dateFormat]="'yy-mm-dd'"
                  [(ngModel)]="endDate"
                  appendTo="body"
                ></p-calendar>
              </div>
            </div>
          </div>
          <div class="col-2 d-flex justify-content-end">
            <div class="col-12 row p-0">
              <div class="col-12 p-0 text-end">
                <div>
                  <p-button
                    label="Run"
                    icon="fa fa-play"
                    iconPos="left"
                    class="primary-button me-3"
                    placement="left"
                    (onClick)="onRun(startDate, endDate)"
                  ></p-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </ng-template>
    <div>
      <div>
        <datagrid
          *ngIf="columnDefs"
          #workflowHistory
          id="workflowHistory"
          [name]="'Workflow History'"
          (gridIsReady)="onGridIsReady($event)"
          [rows]="rowData"
          [columns]="columnDefs"
          [searchValue]="searchValue"
          [showPagination]="true"
          [pageSize]="gridPageSize"
          [hasAutoHeight]="false"
          (cellAction)="onCellAction($event)"
          [showActions]="workflowHistory.ACTION_STATE.VIEW"
        >
        </datagrid>
      </div>
    </div>
  </p-panel>

  <p-dialog
    appendTo="body"
    [header]="workflowTask?.taskType?.workflow?.workflowType?.createdBySystem ? workflowTask?.taskType?.workflow?.workflowType?.name : workflowTask?.taskType?.workflow?.workflowType?.name  + ' - ' + workflowTask?.taskType?.taskName"
    [(visible)]="displayDialog"
    [modal]="true"
    [style]="{ width: '750px' }"
  >
    <div>
      <div *ngIf="workflowTask?.taskType?.workflow?.workflowType?.createdBySystem" class="row justify-content-center">
        <div class="col-5">
          <p-panel header="Current">
            <ng-container [ngTemplateOutlet]="currentTemplate"></ng-container>
          </p-panel>
        </div>
        <div class="col-1"></div>
        <div class="col-5">
          <p-panel header="Requested">
            <ng-container [ngTemplateOutlet]="newTemplate"></ng-container>
          </p-panel>
        </div>
      </div>
      <ng-container *ngIf="!workflowTask?.taskType?.workflow?.workflowType?.createdBySystem" [ngTemplateOutlet]="newTemplate"></ng-container>
    </div>

    <ng-template pTemplate="footer">
      <p-button  *ngIf="!workflowTask?.taskType?.workflow?.workflowType?.createdBySystem && !workflowTask?.done" [disabled]="!justification.trim() || !workflowStep" type="button" data-dismiss="modal" (onClick)="onSendBack()">Send back</p-button>
    </ng-template>
  </p-dialog>
</div>

<ng-template #roleChangeCurrent>
  <ul>
    <li *ngFor="let role of currentData">
      {{ role }}
    </li>
  </ul>
  <span *ngIf="!currentData.length">No role data</span>
</ng-template>

<ng-template #roleChangeNew>
  <ul *ngIf="newData.length">
    <li *ngFor="let role of newData">
      {{ role }}
    </li>
  </ul>
  <span *ngIf="!newData.length">No role data</span>
</ng-template>

<ng-template #hierarchyChangeCurrent>
  <span *ngIf="currentData">{{currentData.hierarchyName}}: {{currentData.hierarchyValue}}</span>
  <span *ngIf="!currentData">No hierarchy data</span>
</ng-template>

<ng-template #hierarchyChangeNew>
  <span>{{newData.hierarchyName}}: {{newData.hierarchyValue}}</span>
</ng-template>

<ng-template #jifChangeNew>
  <div class="row">
    <div class="col-6">
      <ul>
        <ng-container *ngFor="let data of workflowTask?.reqValue; let i = index">
          <li *ngIf="i%2 === 0 && data.field !== 'caseId'">
            <label>{{data.label}}:</label> {{formatValue(workflowTask.value[data.field])}}
          </li>
        </ng-container>
      </ul>
    </div>
    <div class="col-6">
      <ul>
        <ng-container *ngFor="let data of workflowTask?.reqValue; let i = index">
          <li *ngIf="i%2 !== 0 && data.field !== 'caseId'">
            <label>{{data.label}}:</label> {{formatValue(workflowTask.value[data.field])}}
          </li>
        </ng-container>
      </ul>
    </div>
  </div>
  <ng-container *ngIf="!workflowTask?.done">
    <p-divider></p-divider>
    <div class="d-flex mb-2 me-3">
      <div class="d-flex me-2 justify-content-between text-left">
        <label class="label-width m-0 d-flex">Send To*</label>
        <span class="d-flex font-bold">:</span>
      </div>
      <div class="d-flex me-2">
        <p-dropdown
          [options]="workflowPrevSteps"
          [(ngModel)]="workflowStep"
          placeholder="Select Workflow Step"
          optionLabel="name"
          optionValue="code"
        ></p-dropdown>
      </div>
    </div>
    <div class="d-flex mb-2 me-3">
      <div class="d-flex me-2 justify-content-between text-left">
        <label class="label-width m-0 d-flex">Notes*</label>
        <span class="d-flex font-bold">:</span>
      </div>
      <div class="d-flex w-100">
        <textarea rows="3" class="w-100 me-2" pInputTextarea [(ngModel)]="justification"></textarea>
      </div>
    </div>
  </ng-container>
  <ng-container *ngIf="workflowTask?.done">
    <p-divider></p-divider>
    <div class="d-flex mb-2 me-3">
      <div class="d-flex me-2 justify-content-between text-left">
        <label class="label-width m-0 d-flex">Notes*</label>
        <span class="d-flex font-bold">:</span>
      </div>
      <div class="d-flex w-100 overflow-y-auto notes-height">
        <p>{{workflowTask.justification}}</p>
      </div>
    </div>
  </ng-container>
</ng-template>
