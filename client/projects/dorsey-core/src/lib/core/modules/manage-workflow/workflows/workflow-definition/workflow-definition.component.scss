@import 'variables';

:host ::ng-deep {

  .p-button.p-button-icon-only {
    padding: 0.5rem 1.2rem;
  }

  .p-panel .p-panel-header {
    background: $quaternary-color !important;
    color: black !important;
  }

  p-autocomplete .p-autocomplete,
  p-dropdown .p-dropdown {
    width: 100%;
  }

  p-autocomplete .p-autocomplete .p-button {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }

  .p-inputtext {
    width: 100%;
  }

  p-button#add-node {
    display: flex;
    height: 42px;

    .p-button {
      display: flex;
      min-width: 40px;
      padding-left: 12px;
      padding-right: 12px;
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
    }
  }

  p-button#add-node-relation {
    display: flex;
    height: 42px;

    .p-button {
      display: flex;
      min-width: 40px;
      padding-left: 12px;
      padding-right: 12px;
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
    }
  }

  p-button#remove-node-relation {
    display: flex;
    height: 42px;

    .p-button {
      display: flex;
      min-width: 40px;
      padding-left: 12px;
      padding-right: 12px;
      border-radius: 0;
    }
  }

  .ngx-charts-outer {
    max-width: calc(100vw - 190px);
  }

  ngb-tooltip-window {
    z-index: 1001;
  }
}

.card-container {
  height: 100px;
  width: 150px;
  display: flex;
  justify-content: center;

  .name {
    font-size: 14px;
  }

  label {
    display: block;
    align-self: center;
    font-size: 20px;
    margin-top: 4px;
    margin-bottom: 8px;
  }
}

.graph-space {
  width: 100%;
  height: 600px;
}

.apply-gap {
  gap: 15px;
}

.arrow-container {
  width: 50px;
}

.arrow {
  font-size: 24px;
}

//.linkMidpoint {
//  ellipse {
//    fill: white;
//    stroke: black;
//    stroke-width: 1;
//  }
//
//  text {
//    stroke: transparent;
//    fill: black;
//    text-anchor: middle;
//    font-size: 10px;
//  }
//}
