import { ComponentFixture, TestBed } from '@angular/core/testing';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { MessageService } from 'primeng/api';

import { WorkflowDefinitionComponent } from './workflow-definition.component';
import { ToastService } from '../../../../services/toast.service';

describe('WorkflowDefinitionComponent', () => {
  let component: WorkflowDefinitionComponent;
  let fixture: ComponentFixture<WorkflowDefinitionComponent>;

  const mockMessageService = {
    add: jest.fn(),
    clear: jest.fn()
  };

  const mockToastService = {
    displaySuccess: jest.fn(),
    displayError: jest.fn(),
    displayWarning: jest.fn(),
    displayInfo: jest.fn()
  };

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ WorkflowDefinitionComponent ],
      providers: [
        { provide: MessageService, useValue: mockMessageService },
        { provide: ToastService, useValue: mockToastService }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    })
    .compileComponents();

    fixture = TestBed.createComponent(WorkflowDefinitionComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
