import { ComponentFixture, TestBed } from '@angular/core/testing';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';

import { TaskDetailComponent } from './task-detail.component';
import { WorkflowService } from '../../../../services/workflow/workflow.service';

describe('TaskDetailComponent', () => {
  let component: TaskDetailComponent;
  let fixture: ComponentFixture<TaskDetailComponent>;

  const mockWorkflowService = {
    findTaskById: jest.fn(),
    approve: jest.fn(),
    reject: jest.fn(),
    next: jest.fn()
  };

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ TaskDetailComponent ],
      providers: [
        { provide: WorkflowService, useValue: mockWorkflowService }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    })
    .compileComponents();

    fixture = TestBed.createComponent(TaskDetailComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
