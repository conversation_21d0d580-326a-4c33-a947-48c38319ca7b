import { ComponentFixture, TestBed } from '@angular/core/testing';
import { Router } from '@angular/router';
import { TitleCasePipe } from '@angular/common';
import { of } from 'rxjs';

import { WorkflowPanelComponent } from './workflow-panel.component';
import { WorkflowService } from '../../../services/workflow/workflow.service';
import { ToastService } from '../../../services/toast.service';
import { LovService } from '../../../services/lov/lov.service';

describe('WorkflowPanelComponent - Simple Test', () => {
  let component: WorkflowPanelComponent;
  let fixture: ComponentFixture<WorkflowPanelComponent>;

  beforeEach(async () => {
    const workflowServiceSpy = {
      findAssignedToMe: jest.fn().mockReturnValue(of([])),
      findInitiatedByMe: jest.fn().mockReturnValue(of([])),
      findCompleted: jest.fn().mockReturnValue(of([])),
      deleteById: jest.fn().mockReturnValue(of({})),
      softDeleteById: jest.fn().mockReturnValue(of({}))
    };

    const toastServiceSpy = {
      displayError: jest.fn(),
      displaySuccess: jest.fn()
    };

    const lovServiceSpy = {
      findAllWorkflowTypes: jest.fn().mockReturnValue(of([]))
    };

    const routerSpy = {
      navigate: jest.fn()
    };

    const titleCasePipeSpy = {
      transform: jest.fn()
    };

    await TestBed.configureTestingModule({
      declarations: [WorkflowPanelComponent],
      providers: [
        { provide: WorkflowService, useValue: workflowServiceSpy },
        { provide: ToastService, useValue: toastServiceSpy },
        { provide: LovService, useValue: lovServiceSpy },
        { provide: Router, useValue: routerSpy },
        { provide: TitleCasePipe, useValue: titleCasePipeSpy }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(WorkflowPanelComponent);
    component = fixture.componentInstance;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
