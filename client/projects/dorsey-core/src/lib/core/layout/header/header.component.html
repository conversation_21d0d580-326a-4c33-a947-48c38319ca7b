<ng-container [ngPlural]="skin">
  <ng-template ngPluralCase="1">
    <div id="template1" class="d-flex w-100 h-100">
      <div class="d-flex">
        <div class="logo-container-back">
          <div class="logo-container-front">
            <img
              class='logo'
              [src]="logoService.logoUrl"
              alt="cms-logo"
            />
          </div>
        </div>
      </div>
      <div class="header-title-container">
        <div class="header-title">
          <h2>{{title}}</h2>
          <div class="pull-right text-end v-center w-auto me-5">
            <div class="letter-container">
              <fa-icon (click)="router.navigate(['home', 'dashboard'])" [icon]="'envelope'" class="letter" ></fa-icon>
              <div class="notification-container">
                <span>{{ notificationsCount ? notificationsCount : ''}}</span>
              </div>
            </div>
            <fa-icon (click)="router.navigate(['library'])" [icon]="'book'" class="library" ></fa-icon>
            <p-splitButton icon="fa fa-user" [model]="profileItems" styleClass="p-button-text"></p-splitButton>
          </div>
        </div>
        <div class="header-title-separator">
          <p-breadcrumb [model]="items"></p-breadcrumb>
        </div>
      </div>
    </div>
  </ng-template>

  <ng-template ngPluralCase="2">
    <div id="template2" class="d-flex w-100 h-100">
      <div class="d-flex">
        <div class="logo-container-back">
          <div class="logo-container-front">
            <img
              class='logo'
              [src]="logoService.logoUrl"
              alt="cms-logo"
            />
          </div>
        </div>

      </div>
      <div class="header-title-container">
        <div class="header-title">
          <h2 class="mb-5">{{title}}</h2>
          <div class="pull-right v-center w-auto me-3 flex-column mt-3">
            <p-splitButton icon="fa fa-user" [model]="profileItems" styleClass="p-button-text" (onClick)="goProfile()"></p-splitButton>
            <div class="letter-container">
              <fa-icon [icon]="'envelope'" class="letter" ></fa-icon>
              <div class="notification-container">
                <span>{{ notificationsCount ? notificationsCount : ''}}</span>
              </div>
            </div>

          </div>
        </div>
        <div class="header-breadcrumb">
          <p-breadcrumb [model]="items"></p-breadcrumb>
        </div>
      </div>
    </div>
  </ng-template>
</ng-container>




