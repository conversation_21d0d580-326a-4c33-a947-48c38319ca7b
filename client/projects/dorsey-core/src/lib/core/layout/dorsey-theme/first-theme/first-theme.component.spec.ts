import { ComponentFixture, TestBed } from '@angular/core/testing';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';

import { FirstThemeComponent } from './first-theme.component';
import { HeaderService } from '../../../services/admin/header.service';
import { MenuItem } from 'primeng/api';

describe('FirstThemeComponent', () => {
  let component: FirstThemeComponent;
  let fixture: ComponentFixture<FirstThemeComponent>;
  let mockHeaderService: jest.Mocked<HeaderService>;

  const mockMenuItems: MenuItem[] = [
    { label: 'Home', icon: 'fa-home', routerLink: '/home' },
    { label: 'Admin', icon: 'fa-cog', routerLink: '/admin' }
  ];

  beforeEach(async () => {
    mockHeaderService = {
      selectedMenuOption: '',
      skin: 1
    } as any;

    await TestBed.configureTestingModule({
      declarations: [FirstThemeComponent],
      providers: [
        { provide: HeaderService, useValue: mockHeaderService }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();

    fixture = TestBed.createComponent(FirstThemeComponent);
    component = fixture.componentInstance;
  });

  describe('Component Initialization', () => {
    it('should create', () => {
      expect(component).toBeTruthy();
    });

    it('should initialize with default values', () => {
      expect(component.display).toBe(false);
      expect(component.initialRoute).toBeUndefined();
    });

    it('should inject HeaderService', () => {
      expect(component.headerService).toBe(mockHeaderService);
    });

    it('should accept menuItems input', () => {
      component.menuItems = mockMenuItems;
      expect(component.menuItems).toBe(mockMenuItems);
    });
  });

  describe('ngOnInit', () => {
    it('should call ngOnInit without errors', () => {
      expect(() => {
        component.ngOnInit();
      }).not.toThrow();
    });

    it('should not modify component state in ngOnInit', () => {
      const initialDisplay = component.display;
      const initialRoute = component.initialRoute;

      component.ngOnInit();

      expect(component.display).toBe(initialDisplay);
      expect(component.initialRoute).toBe(initialRoute);
    });
  });

  describe('ngAfterViewChecked', () => {
    it('should not load breadcrumb when display is false', () => {
      component.display = false;
      component.initialRoute = '/test/route';
      component.header = { loadBreadcrumb: jest.fn() } as any;

      component.ngAfterViewChecked();

      expect(component.header.loadBreadcrumb).not.toHaveBeenCalled();
      expect(component.initialRoute).toBe('/test/route');
    });

    it('should not load breadcrumb when initialRoute is null', () => {
      component.display = true;
      component.initialRoute = null;
      component.header = { loadBreadcrumb: jest.fn() } as any;

      component.ngAfterViewChecked();

      expect(component.header.loadBreadcrumb).not.toHaveBeenCalled();
    });

    it('should load breadcrumb when display is true and initialRoute exists', () => {
      component.display = true;
      component.initialRoute = '/test/route';
      component.header = { loadBreadcrumb: jest.fn() } as any;

      component.ngAfterViewChecked();

      expect(component.header.loadBreadcrumb).toHaveBeenCalledWith('/test/route');
      expect(component.initialRoute).toBeNull();
    });

    it('should handle missing header component gracefully', () => {
      component.display = true;
      component.initialRoute = '/test/route';
      component.header = undefined;

      expect(() => {
        component.ngAfterViewChecked();
      }).not.toThrow();
    });
  });

  describe('ViewChild References', () => {
    it('should have header ViewChild reference', () => {
      expect(component.header).toBeUndefined();
    });

    it('should have menu ViewChild reference', () => {
      expect(component.menu).toBeUndefined();
    });
  });

  describe('AbstractTheme Implementation', () => {
    it('should implement AbstractTheme interface', () => {
      // The component should have the required properties for AbstractTheme
      expect(component.header).toBeDefined();
      expect(component.menu).toBeDefined();
    });
  });

  describe('Component State Management', () => {
    it('should allow display property to be modified', () => {
      component.display = true;
      expect(component.display).toBe(true);

      component.display = false;
      expect(component.display).toBe(false);
    });

    it('should allow initialRoute property to be modified', () => {
      component.initialRoute = '/new/route';
      expect(component.initialRoute).toBe('/new/route');

      component.initialRoute = null;
      expect(component.initialRoute).toBeNull();
    });
  });

  describe('Template Integration', () => {
    it('should render without errors', () => {
      component.menuItems = mockMenuItems;

      expect(() => {
        fixture.detectChanges();
      }).not.toThrow();
    });

    it('should maintain component state after template rendering', () => {
      component.display = true;
      component.initialRoute = '/test';

      fixture.detectChanges();

      expect(component.display).toBe(true);
      expect(component.initialRoute).toBe('/test');
    });
  });

  describe('Edge Cases', () => {
    it('should handle empty menuItems array', () => {
      component.menuItems = [];

      expect(() => {
        fixture.detectChanges();
      }).not.toThrow();
    });

    it('should handle undefined menuItems', () => {
      component.menuItems = undefined;

      expect(() => {
        fixture.detectChanges();
      }).not.toThrow();
    });

    it('should handle multiple ngAfterViewChecked calls', () => {
      component.display = true;
      component.initialRoute = '/test/route';
      component.header = { loadBreadcrumb: jest.fn() } as any;

      component.ngAfterViewChecked();
      component.ngAfterViewChecked();

      expect(component.header.loadBreadcrumb).toHaveBeenCalledTimes(1);
      expect(component.initialRoute).toBeNull();
    });
  });
});
