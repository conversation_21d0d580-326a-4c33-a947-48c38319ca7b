import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { HeaderComponent } from './header/header.component';
import { Menu3Component } from './menu/menu.component';
import { FooterComponent } from './footer/footer.component';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { SplitButtonModule } from 'primeng/splitbutton';
import { BreadcrumbModule } from 'primeng/breadcrumb';
import {
  NgbAccordionModule,
  NgbTooltipModule,
} from '@ng-bootstrap/ng-bootstrap';
import { ThirdThemeComponent } from './third-theme.component';
import { SharedModule } from '../../../modules/shared.module';
import { DividerModule } from 'primeng/divider';
import { SpeedDialModule } from 'primeng/speeddial';
import { FormsModule } from '@angular/forms';
import { InputTextModule } from 'primeng/inputtext';

@NgModule({
  declarations: [
    ThirdThemeComponent,
    HeaderComponent,
    Menu3Component,
    FooterComponent,
  ],
  providers: [],
  imports: [
    SharedModule,
    CommonModule,
    FontAwesomeModule,
    SplitButtonModule,
    BreadcrumbModule,
    NgbAccordionModule,
    DividerModule,
    SpeedDialModule,
    NgbTooltipModule,
    FormsModule,
    InputTextModule,
  ],
  exports: [ThirdThemeComponent],
})
export class ThirdThemeModule {}
