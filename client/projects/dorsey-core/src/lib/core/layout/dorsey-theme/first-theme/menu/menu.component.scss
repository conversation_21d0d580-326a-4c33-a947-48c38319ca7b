@import 'variables';

$chevron-size: 16px;

.menu-container {
  flex: none;
  position: relative;
  display: flex;
  min-width: $menu-width;
  background-color: $primary-color;
  color: $menu-icons-color;
  z-index: 1000;
  border-bottom-left-radius: 20px;
}

.menu-separator {
  display: inline;
  min-width: 5px;
  height: 100%;
  background-color: $quaternary-color;
  //z-index: -1;
  position: absolute;
  margin-left: 52px;
  transition: all 0s;
  transition-delay: 0.3s;
  opacity: 1;
}

div.opened .menu-separator {
  opacity: 0;
  transition-delay: 0s;
  transition: all 0s;
}

div[ngbaccordion]:not(.closed) .option {
  border-right-width: 0;
  transition-delay: 0s;
  transition: all 0s;
}

.option {
  transition: all 0s;
  transition-delay: 0.3s;
  border-right: 5px solid $quaternary-color;
}

.option-closed {
  padding-left: 8px !important;
}

.sticky {
  position: sticky;
  top: 0;
  height: fit-content;
  overflow: hidden;
}

:host {
  height: 100%;
  display: flex;
  border-bottom-left-radius: 20px;
  background-color: $primary-color;

  :hover {
    z-index: 1001;
  }

  .ng-animating {
    z-index: 1001;
  }

  ::ng-deep {

    fa-icon {
      margin: 5px 8px 5px 5px;
      font-size: $chevron-size;
    }

    fa-icon:hover {
      cursor: pointer;
    }

    div.text-end {
      cursor: pointer;
    }

    .accordion-button, .option {
      padding: 8px;
      background-color: $primary-color !important;
      box-shadow: none !important;
      color: $menu-icons-color !important;
      justify-content: space-between;
    }

    .accordion-button::after {
      background-size: $chevron-size;
      width: $chevron-size;
      height: $chevron-size;
      margin-left: 5px;
    }

    .accordion-item {
      background-color: $primary-color;
      border-style: none;
    }

    span {
      visibility: visible;
      font-weight: 400;
      user-select: none;
      white-space: nowrap;
    }

    .closed span {
      visibility: hidden;
      opacity: 0;
      transition-duration: 0.3s !important;
    }

    i {
      font-size: 24px !important;
      margin: 0 7px 0 7px;
      display: inline !important;
      cursor: pointer;
    }

    .selected i {
      color: $tertiary-color;
    }

    div[ngbaccordion].closed .accordion-header {
      border-right: 5px solid $quaternary-color;
      transition: all 0s;
      transition-delay: 0.3s;
      padding-left: 0;
    }

    .accordion-header {
      border-right-width: 0;
      transition-delay: 0s;
      transition: all 0s;

      > button[disabled] {
        cursor: pointer;
      }

      > button[disabled]:hover {
        background-color: $menu-option-hover-color !important;
        color: $primary-color !important;
      }
    }

    .accordion-body {
      padding: 0;

      .option, .accordion-header {
        height: 35px;
        padding: 5px 20px 0 25px;
        cursor: pointer;
      }

      > .option:hover {
        background-color: $menu-option-hover-color !important;
        color: $primary-color !important;
      }
    }

    .accordion-button[disabled]::after {
      display: none;
    }

    .accordion-button::after,
    .accordion-button:not(.collapsed)::after {
      background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23ffffff'%3e%3cpath fill-rule='evenodd' stroke='white' stroke-width='1' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
    }

    .selected {
      background-color: $primary-color;
      color: $tertiary-color;
      border-right: 5px solid $tertiary-color !important;
    }

    .inner-accordion {

      .accordion-button {
        padding: 0 8px 0 0;
      }

      .accordion-body {
        div:not(.option-closed) {
          padding-left: 40px !important;
        }
      }
    }
  }
}
