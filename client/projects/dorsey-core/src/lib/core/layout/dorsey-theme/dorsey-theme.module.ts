import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FirstThemeModule } from './first-theme/first-theme.module';
import { SecondThemeModule } from './second-theme/second-theme.module';
import { ThirdThemeModule } from './third-theme/third-theme.module';
import { FourthThemeModule } from './fourth-theme/fourth-theme.module';
import { DorseyThemeComponent } from './dorsey-theme.component';
import { CoreModule } from '../../core.module';

@NgModule({
  declarations: [DorseyThemeComponent],
  imports: [
    CommonModule,
    FirstThemeModule,
    SecondThemeModule,
    ThirdThemeModule,
    FourthThemeModule,
  ],
  exports: [DorseyThemeComponent],
})
export class DorseyThemeModule {}
