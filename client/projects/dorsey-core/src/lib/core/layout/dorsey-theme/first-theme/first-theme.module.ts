import { NgModule } from '@angular/core';
import { HeaderComponent } from './header/header.component';
import { Menu1Component } from './menu/menu.component';
import { FooterComponent } from './footer/footer.component';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { SplitButtonModule } from 'primeng/splitbutton';
import { BreadcrumbModule } from 'primeng/breadcrumb';
import { FirstThemeComponent } from './first-theme.component';
import { NgbAccordionModule } from '@ng-bootstrap/ng-bootstrap';
import { SharedModule } from '../../../modules/shared.module';
import { CommonModule } from '@angular/common';

@NgModule({
  declarations: [
    FirstThemeComponent,
    HeaderComponent,
    Menu1Component,
    FooterComponent,
  ],
  imports: [
    SharedModule,
    CommonModule,
    FontAwesomeModule,
    SplitButtonModule,
    BreadcrumbModule,
    NgbAccordionModule,
  ],
  exports: [FirstThemeComponent],
})
export class FirstThemeModule {}
