<div id="template" class="d-flex w-100 h-100">
  <div class="d-flex">
    <div class="logo-container-back">
      <div class="logo-container-front">
        <img
          class='logo'
          [src]="headerService.logoUrl"
          alt="cms-logo"
        />
      </div>
    </div>

  </div>
  <div class="header-title-container">
    <div class="header-title">
      <h2 class="mb-5">{{headerService.title}}</h2>
      <div class="pull-right v-center w-auto me-3 flex-column mt-3">
        <p-splitButton icon="fa fa-user" [model]="profileItems" styleClass="p-button-text" (onClick)="goProfile()"></p-splitButton>
        <div class="letter-container">
          <fa-icon [icon]="'envelope'" class="letter" ></fa-icon>
          <div class="notification-container">
            <span>{{ notificationsCount ? notificationsCount : ''}}</span>
          </div>
        </div>

      </div>
    </div>
    <div class="header-breadcrumb">
      <p-breadcrumb [model]="headerService.breadCrumbItems"></p-breadcrumb>
    </div>
  </div>
</div>

