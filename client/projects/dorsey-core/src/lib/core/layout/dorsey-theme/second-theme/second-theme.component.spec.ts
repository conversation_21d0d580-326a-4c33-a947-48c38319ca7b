import { ComponentFixture, TestBed } from '@angular/core/testing';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';

import { SecondThemeComponent } from './second-theme.component';
import { HeaderService } from '../../../services/header.service';
import { MenuItem } from '../../../models/menu-item';

describe('SecondThemeComponent', () => {
  let component: SecondThemeComponent;
  let fixture: ComponentFixture<SecondThemeComponent>;
  let mockHeaderService: jest.Mocked<HeaderService>;

  const mockMenuItems: MenuItem[] = [
    { label: 'Home', icon: 'fa-home', routerLink: '/home', items: [] },
    { label: 'Admin', icon: 'fa-cog', routerLink: '/admin', items: [] }
  ];

  beforeEach(async () => {
    mockHeaderService = {
      selectedMenuOption: '',
      skin: 2
    } as any;

    await TestBed.configureTestingModule({
      declarations: [SecondThemeComponent],
      providers: [
        { provide: HeaderService, useValue: mockHeaderService }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();

    fixture = TestBed.createComponent(SecondThemeComponent);
    component = fixture.componentInstance;
  });

  describe('Component Initialization', () => {
    it('should create', () => {
      expect(component).toBeTruthy();
    });

    it('should initialize with default values', () => {
      expect(component.display).toBe(false);
      expect(component.initialRoute).toBeUndefined();
    });

    it('should inject HeaderService', () => {
      expect(component.headerService).toBe(mockHeaderService);
    });

    it('should accept menuItems input', () => {
      component.menuItems = mockMenuItems;
      expect(component.menuItems).toBe(mockMenuItems);
    });
  });

  describe('ngOnInit', () => {
    it('should call ngOnInit without errors', () => {
      expect(() => {
        component.ngOnInit();
      }).not.toThrow();
    });
  });

  describe('ngAfterViewChecked', () => {
    it('should load breadcrumb when display is true and initialRoute exists', () => {
      component.display = true;
      component.initialRoute = '/test/route';
      component.header = { loadBreadcrumb: jest.fn() } as any;

      component.ngAfterViewChecked();

      expect(component.header.loadBreadcrumb).toHaveBeenCalledWith('/test/route');
      expect(component.initialRoute).toBeNull();
    });

    it('should not load breadcrumb when conditions are not met', () => {
      component.display = false;
      component.initialRoute = '/test/route';
      component.header = { loadBreadcrumb: jest.fn() } as any;

      component.ngAfterViewChecked();

      expect(component.header.loadBreadcrumb).not.toHaveBeenCalled();
    });
  });

  describe('AbstractTheme Implementation', () => {
    it('should implement AbstractTheme interface', () => {
      expect(component.header).toBeDefined();
      expect(component.menu).toBeDefined();
    });
  });

  describe('Template Integration', () => {
    it('should render without errors', () => {
      component.menuItems = mockMenuItems;

      expect(() => {
        fixture.detectChanges();
      }).not.toThrow();
    });
  });
});
