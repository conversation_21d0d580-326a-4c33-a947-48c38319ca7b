import { ComponentFixture, TestBed } from '@angular/core/testing';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';

import { FourthThemeComponent } from './fourth-theme.component';
import { HeaderService } from '../../../services/header.service';
import { MenuItem } from '../../../models/menu-item';

describe('FourthThemeComponent', () => {
  let component: FourthThemeComponent;
  let fixture: ComponentFixture<FourthThemeComponent>;
  let mockHeaderService: jest.Mocked<HeaderService>;

  beforeEach(async () => {
    mockHeaderService = { selectedMenuOption: '', skin: 4 } as any;

    await TestBed.configureTestingModule({
      declarations: [FourthThemeComponent],
      providers: [{ provide: HeaderService, useValue: mockHeaderService }],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();

    fixture = TestBed.createComponent(FourthThemeComponent);
    component = fixture.componentInstance;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with default values', () => {
    expect(component.display).toBe(false);
    expect(component.initialRoute).toBeUndefined();
  });

  it('should inject HeaderService', () => {
    expect(component.headerService).toBe(mockHeaderService);
  });

  it('should call ngOnInit without errors', () => {
    expect(() => component.ngOnInit()).not.toThrow();
  });

  it('should load breadcrumb when conditions are met', () => {
    component.display = true;
    component.initialRoute = '/test';
    component.header = { loadBreadcrumb: jest.fn() } as any;

    component.ngAfterViewChecked();

    expect(component.header.loadBreadcrumb).toHaveBeenCalledWith('/test');
    expect(component.initialRoute).toBeNull();
  });

  it('should render without errors', () => {
    expect(() => fixture.detectChanges()).not.toThrow();
  });
});
