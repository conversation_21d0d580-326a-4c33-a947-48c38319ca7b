import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { HeaderComponent } from './header/header.component';
import { Menu4Component } from './menu/menu.component';
import { FooterComponent } from './footer/footer.component';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { SplitButtonModule } from 'primeng/splitbutton';
import { BreadcrumbModule } from 'primeng/breadcrumb';
import {
  NgbAccordionModule,
  NgbTooltipModule,
} from '@ng-bootstrap/ng-bootstrap';
import { FourthThemeComponent } from './fourth-theme.component';
import { SharedModule } from '../../../modules/shared.module';
import { CoreModule } from '../../../core.module';
import { DividerModule } from 'primeng/divider';
import { InputTextModule } from 'primeng/inputtext';
import { SpeedDialModule } from 'primeng/speeddial';
import { FormsModule } from '@angular/forms';

@NgModule({
  declarations: [
    FourthThemeComponent,
    HeaderComponent,
    Menu4Component,
    FooterComponent,
  ],
  imports: [
    SharedModule,
    CommonModule,
    FontAwesomeModule,
    SplitButtonModule,
    BreadcrumbModule,
    NgbAccordionModule,
    DividerModule,
    SpeedDialModule,
    NgbTooltipModule,
    FormsModule,
    InputTextModule,
  ],
  exports: [FourthThemeComponent],
})
export class FourthThemeModule {}
