import { Component, OnInit, TemplateRef } from '@angular/core';
import { DialogMessageService } from '../../services/dialog-message.service';
import { DialogMessage } from '../../models/dialog-message.model';
import { Subscription } from 'rxjs';

@Component({
  selector: 'dorsey-dialog-message',
  templateUrl: './dialog-message.component.html',
  styleUrls: ['./dialog-message.component.scss'],
})
export class DialogMessageComponent implements OnInit {
  message: DialogMessage;
  subscription: Subscription;
  customBody: TemplateRef<any>;

  constructor(private dialogMessageService: DialogMessageService) {
    this.subscription = this.dialogMessageService.message.subscribe(
      (message) => {
        this.message = message;

        if (message.body instanceof TemplateRef) {
          this.customBody = message.body;
        }
      }
    );
  }

  ngOnInit(): void {}

  onCancel() {
    if (this.message.cancelCallBack) {
      this.message.cancelCallBack();
    }
    this.message = undefined;
    this.customBody = undefined;
  }

  onAccept() {
    if (this.message.okCallBack) {
      this.message.okCallBack();
    }
    this.message = undefined;
    this.customBody = undefined;
  }

  ngOnDestroy() {
    this.subscription.unsubscribe();
  }
}
