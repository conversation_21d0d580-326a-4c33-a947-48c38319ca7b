<ng-container *ngIf="message">
  <ng-container>
    <div class="modal" role="alertdialog" id="dm-modal-overlay" aria-labelledby="dm-dialog-title" aria-modal="true">
      <div class="modal-dialog" role="document">
        <div class="modal-content">
          <div class="modal-header">
            <h5 [innerHTML]="message.title"></h5>
          </div>

          <div class="modal-body">
            <ng-template *ngIf="customBody" [ngTemplateOutlet]="customBody"></ng-template>
            <ng-container *ngIf="!customBody">
              <p [innerHtml]="message.body"></p>
            </ng-container>
          </div>

          <div class="modal-footer justify-content-between">
            <p-button type="button" data-dismiss="modal" (onClick)="onAccept()">
              {{ message.ctaLabel }}
            </p-button>
            <div *ngIf="!message.hasCancel">&nbsp;</div>
            <dorsey-cancel-cta
              data-dismiss="modal"
              *ngIf="message.hasCancel"
              (onCancelClicked)="onCancel()"
            ></dorsey-cancel-cta>
          </div>
        </div>
      </div>
    </div>
  </ng-container>
</ng-container>
