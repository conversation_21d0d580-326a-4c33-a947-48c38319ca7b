import { ComponentFixture, TestBed } from '@angular/core/testing';
import { Router } from '@angular/router';
import { ActivatedRoute } from '@angular/router';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { of, throwError, Subject } from 'rxjs';
import { GridApi } from 'ag-grid-community';

import { RoleListComponent } from './role-list.component';
import { RoleService } from '../../../services/admin/role.service';
import { ToastService } from '../../../services/toast.service';
import { EditingStateService } from '../../../services/editing-state.service';
import { DialogMessageService } from '../../../services/dialog-message.service';
import { IRole } from '../../../models/admin/roles/role.model';
import { RoleActions } from '../../../models/enums/role-actions';
import { FormAction } from '../../../models/form-action';
import { DataGridMessage } from '../../datagrid/models/datagrid-message.model';
import { DatagridActionsCta } from '../../datagrid/models/enums/datagrid-actions-cta';
import { GridRow } from '../../datagrid/models/grid-row.model';
import { ActionState } from '../../datagrid/models/action-state';
import { IRoleCapabilities } from '../../../models/admin/roles/role-capabilities';
import * as gridUtils from '../../../utils/grid-utils';

describe('RoleListComponent', () => {
  let component: RoleListComponent;
  let fixture: ComponentFixture<RoleListComponent>;
  let mockRoleService: jest.Mocked<RoleService>;
  let mockToastService: jest.Mocked<ToastService>;
  let mockEditingStateService: jest.Mocked<EditingStateService>;
  let mockDialogMessageService: jest.Mocked<DialogMessageService>;
  let mockRouter: jest.Mocked<Router>;
  let mockActivatedRoute: jest.Mocked<ActivatedRoute>;

  const mockRoles: IRole[] = [
    {
      roleId: '1',
      name: 'Admin',
      department: 'IT',
      description: 'Administrator role',
      superior: null,
      superiorId: null,
      capabilities: []
    },
    {
      roleId: '2',
      name: 'Manager',
      department: 'Sales',
      description: 'Sales manager role',
      superior: { roleId: '1', name: 'Admin' },
      superiorId: '1',
      capabilities: []
    },
    {
      roleId: '3',
      name: 'Employee',
      department: 'Sales',
      description: 'Sales employee role',
      superior: { roleId: '2', name: 'Manager' },
      superiorId: '2',
      capabilities: []
    }
  ];

  beforeEach(async () => {
    const mockEditingStateSubject = new Subject<FormAction>();

    mockRoleService = {
      findAll: jest.fn().mockReturnValue(of(mockRoles)),
      updateRole: jest.fn().mockReturnValue(of({}))
    } as any;

    mockToastService = {
      displaySuccess: jest.fn(),
      displayError: jest.fn()
    } as any;

    mockEditingStateService = {
      getValue: jest.fn().mockReturnValue(mockEditingStateSubject.asObservable()),
      setValue: jest.fn(),
      setData: jest.fn(),
      editingState: {
        isEditing: false,
        hasChanges: false
      }
    } as any;

    mockDialogMessageService = {
      displayError: jest.fn()
    } as any;

    mockRouter = {
      navigate: jest.fn().mockResolvedValue(true),
      navigateByUrl: jest.fn().mockResolvedValue(true)
    } as any;

    mockActivatedRoute = {
      snapshot: { params: {}, queryParams: {} },
      params: of({}),
      queryParams: of({})
    } as any;

    await TestBed.configureTestingModule({
      declarations: [RoleListComponent],
      providers: [
        { provide: RoleService, useValue: mockRoleService },
        { provide: ToastService, useValue: mockToastService },
        { provide: EditingStateService, useValue: mockEditingStateService },
        { provide: DialogMessageService, useValue: mockDialogMessageService },
        { provide: Router, useValue: mockRouter },
        { provide: ActivatedRoute, useValue: mockActivatedRoute }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();

    fixture = TestBed.createComponent(RoleListComponent);
    component = fixture.componentInstance;
  });

  describe('Component Initialization', () => {
    it('should create', () => {
      expect(component).toBeTruthy();
    });

    it('should initialize with default values', () => {
      expect(component.title).toBe('Role List');
      expect(component.roleAction).toBe(RoleActions);
      expect(component.colors.length).toBe(8);
      expect(component.departmentColorMap).toBeInstanceOf(Map);
      expect(component.nodes).toEqual([]);
      expect(component.links).toEqual([]);
      expect(component.errors).toEqual([]);
      expect(component.defCapabilities).toEqual([]);
      expect(component.WAIT_GRAPH_MS).toBe(1000);
      expect(component.canAdjust).toBe(true);
    });

    it('should have correct layout settings', () => {
      expect(component.layoutSettings.orientation).toBe('TB');
      expect(component.curve).toBeDefined();
      expect(component.layout).toBeDefined();
    });
  });

  describe('ngOnInit', () => {
    it('should call loadData on initialization', () => {
      jest.spyOn(component as any, 'loadData').mockImplementation(() => {});

      component.ngOnInit();

      expect(component['loadData']).toHaveBeenCalled();
    });

    it('should initialize defCapabilities', () => {
      jest.spyOn(component as any, 'loadData').mockImplementation(() => {});

      component.ngOnInit();

      expect(component.defCapabilities).toBeDefined();
    });
  });

  describe('Data Loading', () => {
    it('should load roles successfully', () => {
      jest.spyOn(component, 'setColumnDefinition').mockImplementation(() => {});
      jest.spyOn(component, 'loadTree').mockImplementation(() => {});
      jest.spyOn(component, 'adjustGraph').mockImplementation(() => {});

      component['loadData']();

      expect(mockRoleService.findAll).toHaveBeenCalled();
      expect(component.rowData).toEqual(mockRoles);
      expect(component.setColumnDefinition).toHaveBeenCalled();
      expect(component.loadTree).toHaveBeenCalled();
      expect(component.adjustGraph).toHaveBeenCalledWith(false);
    });

    it('should handle load error', () => {
      mockRoleService.findAll.mockReturnValue(throwError('Load error'));

      component['loadData']();

      expect(mockToastService.displayError).toHaveBeenCalledWith('Roles load failed.');
    });
  });

  describe('Column Definition', () => {
    it('should set column definitions correctly', () => {
      component.setColumnDefinition();

      expect(component.columnDefs).toBeDefined();
      expect(component.columnDefs.length).toBe(4);
      expect(component.columnDefs[0].field).toBe('name');
      expect(component.columnDefs[1].field).toBe('department');
      expect(component.columnDefs[2].field).toBe('superior.name');
      expect(component.columnDefs[3].field).toBe('description');
    });

    it('should configure editable fields correctly', () => {
      component.setColumnDefinition();

      const nameColumn = component.columnDefs[0];
      const departmentColumn = component.columnDefs[1];
      const descriptionColumn = component.columnDefs[3];

      // Admin role should not be editable
      expect(nameColumn.editable({ data: { name: 'Admin' } })).toBe(false);
      expect(departmentColumn.editable({ data: { name: 'Admin' } })).toBe(false);
      expect(descriptionColumn.editable({ data: { name: 'Admin' } })).toBe(false);

      // Other roles should be editable
      expect(nameColumn.editable({ data: { name: 'Manager' } })).toBe(true);
      expect(departmentColumn.editable({ data: { name: 'Manager' } })).toBe(true);
      expect(descriptionColumn.editable({ data: { name: 'Manager' } })).toBe(true);
    });

    it('should configure cell classes correctly', () => {
      component.setColumnDefinition();

      const nameColumn = component.columnDefs[0];

      expect(nameColumn.cellClass({ data: { name: 'Admin' } })).toBe('cell-not-editable');
      expect(nameColumn.cellClass({ data: { name: 'Manager' } })).toBe('');
    });
  });

  describe('Grid Operations', () => {
    it('should handle grid ready event', () => {
      const mockGridApi = { test: 'api' } as any;

      component.onGridIsReady(mockGridApi);

      expect(component.gridApi).toBe(mockGridApi);
    });

    it('should create new row with default values', () => {
      const newRow = component.onNewRow();

      expect(newRow).toEqual({
        name: '',
        department: '',
        superior: null,
        description: ''
      });
    });
  });

  describe('Tree Operations', () => {
    beforeEach(() => {
      component.rowData = mockRoles;
    });

    it('should load tree nodes and links', () => {
      component.loadTree();

      expect(component.nodes.length).toBe(3);
      expect(component.links.length).toBe(2); // Two roles have superiors
      expect(component.departmentColorMap.size).toBe(2); // IT and Sales departments
    });

    it('should assign colors to departments', () => {
      component.loadTree();

      expect(component.departmentColorMap.has('IT')).toBe(true);
      expect(component.departmentColorMap.has('Sales')).toBe(true);
      expect(component.departmentColorMap.get('IT')).toBe(component.colors[0]);
      expect(component.departmentColorMap.get('Sales')).toBe(component.colors[1]);
    });

    it('should create nodes with correct structure', () => {
      component.loadTree();

      const adminNode = component.nodes.find(n => n.id === '1');
      expect(adminNode).toBeDefined();
      expect(adminNode?.label).toBe('Admin');
      expect(adminNode?.data.department).toBe('IT');
      expect(adminNode?.data.backgroundColor).toBe(component.colors[0]);
    });

    it('should create edges for superior relationships', () => {
      component.loadTree();

      const managerEdge = component.links.find(l => l.target === '2');
      expect(managerEdge).toBeDefined();
      expect(managerEdge?.source).toBe('1');

      const employeeEdge = component.links.find(l => l.target === '3');
      expect(employeeEdge).toBeDefined();
      expect(employeeEdge?.source).toBe('2');
    });

    it('should get styles for nodes', () => {
      const mockNode = {
        id: '1',
        label: 'Test',
        data: { backgroundColor: '#BDE8A5' }
      };

      const styles = component.getStyles(mockNode);

      expect(styles).toEqual({
        'background-color': '#BDE8A5'
      });
    });
  });

  describe('Cell Actions', () => {
    it('should handle VIEW action', () => {
      const mockMessage: DataGridMessage = {
        action: DatagridActionsCta.VIEW,
        rawData: { roleId: '123' }
      } as any;

      component.onCellAction(mockMessage);

      expect(mockRouter.navigate).toHaveBeenCalledWith(['admin', 'role-list', 'details'], {
        queryParams: { roleId: '123' }
      });
    });

    it('should ignore unknown actions', () => {
      const mockMessage: DataGridMessage = {
        action: 'UNKNOWN_ACTION' as any,
        rawData: { roleId: '123' }
      } as any;

      expect(() => {
        component.onCellAction(mockMessage);
      }).not.toThrow();
    });
  });

  describe('Graph Adjustment', () => {
    beforeEach(() => {
      jest.useFakeTimers();
    });

    afterEach(() => {
      jest.useRealTimers();
    });

    it('should adjust graph when canAdjust is true', () => {
      component.canAdjust = true;
      jest.spyOn(component.update$, 'next');
      jest.spyOn(component.center$, 'next');
      jest.spyOn(component.zoomToFit$, 'next');

      component.adjustGraph(false);

      expect(component.zoomOk).toBe(false);

      jest.advanceTimersByTime(1000);

      expect(component.update$.next).toHaveBeenCalledWith(true);
      expect(component.center$.next).toHaveBeenCalledWith(true);
      expect(component.zoomToFit$.next).toHaveBeenCalledWith(true);
      expect(component.zoomOk).toBe(true);
    });

    it('should not adjust graph when canAdjust is false', () => {
      component.canAdjust = false;
      jest.spyOn(component.update$, 'next');

      component.adjustGraph(false);

      jest.advanceTimersByTime(1000);

      expect(component.update$.next).not.toHaveBeenCalled();
    });

    it('should prevent adjustment when zoomChanged is true', () => {
      component.canAdjust = true;

      component.adjustGraph(true);

      expect(component.canAdjust).toBe(false);

      jest.advanceTimersByTime(1000);

      expect(component.canAdjust).toBe(true);
    });
  });

  describe('Data Validation', () => {
    beforeEach(() => {
      component.gridApi = {
        forEachNode: jest.fn((callback) => {
          mockRoles.forEach((role, index) => {
            callback({ data: { ...role, rowId: index } });
          });
        })
      } as any;
    });

    it('should validate successfully with valid data', () => {
      const validRoles = [
        { name: 'Manager', department: 'Sales', description: 'Sales manager', rowId: 0 },
        { name: 'Employee', department: 'IT', description: 'IT employee', rowId: 1 }
      ];

      component.gridApi = {
        forEachNode: jest.fn((callback) => {
          validRoles.forEach(role => callback({ data: role }));
        })
      } as any;

      const result = component['validateData']();

      expect(result).toBe(true);
      expect(component.errors).toEqual([]);
    });

    it('should fail validation for empty role name', () => {
      const invalidRoles = [
        { name: '', department: 'Sales', description: 'Sales manager', rowId: 0 }
      ];

      component.gridApi = {
        forEachNode: jest.fn((callback) => {
          invalidRoles.forEach(role => callback({ data: role }));
        })
      } as any;

      const result = component['validateData']();

      expect(result).toBe(false);
      expect(mockDialogMessageService.displayError).toHaveBeenCalled();
    });

    it('should fail validation for duplicate role names', () => {
      const duplicateRoles = [
        { name: 'Manager', department: 'Sales', description: 'Sales manager', rowId: 0 },
        { name: 'manager', department: 'IT', description: 'IT manager', rowId: 1 }
      ];

      component.gridApi = {
        forEachNode: jest.fn((callback) => {
          duplicateRoles.forEach(role => callback({ data: role }));
        })
      } as any;

      const result = component['validateData']();

      expect(result).toBe(false);
      expect(mockDialogMessageService.displayError).toHaveBeenCalled();
    });

    it('should fail validation for too many departments', () => {
      const manyDepartments = Array.from({ length: 8 }, (_, i) => ({
        name: `Role${i}`,
        department: `Dept${i}`,
        description: `Description${i}`,
        rowId: i
      }));

      component.gridApi = {
        forEachNode: jest.fn((callback) => {
          manyDepartments.forEach(role => callback({ data: role }));
        })
      } as any;

      const result = component['validateData']();

      expect(result).toBe(false);
      expect(mockDialogMessageService.displayError).toHaveBeenCalled();
    });
  });

  describe('Save Data', () => {
    beforeEach(() => {
      component.gridApi = {
        forEachNode: jest.fn((callback) => {
          const validRole = { name: 'Manager', department: 'Sales', description: 'Sales manager', rowId: 0 };
          callback({ data: validRole });
        })
      } as any;
      component.defCapabilities = [{ id: '1', name: 'Test Capability' }] as any;
    });

    it('should save data successfully when validation passes', () => {
      jest.spyOn(component as any, 'validateData').mockReturnValue(true);

      component['saveData']();

      expect(mockRoleService.updateRole).toHaveBeenCalled();
      expect(mockToastService.displaySuccess).toHaveBeenCalledWith('Roles saved successfully.');
      expect(mockEditingStateService.setValue).toHaveBeenCalledWith(FormAction.SUBMIT);
    });

    it('should not save data when validation fails', () => {
      jest.spyOn(component as any, 'validateData').mockReturnValue(false);

      component['saveData']();

      expect(mockRoleService.updateRole).not.toHaveBeenCalled();
    });

    it('should handle save error', () => {
      jest.spyOn(component as any, 'validateData').mockReturnValue(true);
      mockRoleService.updateRole.mockReturnValue(throwError('Save error'));

      component['saveData']();

      expect(mockToastService.displayError).toHaveBeenCalledWith('Failed while saving Role data.');
    });

    it('should assign capabilities to roles before saving', () => {
      jest.spyOn(component as any, 'validateData').mockReturnValue(true);
      const mockCapabilities = [{ id: '1', name: 'Test' }];
      component.defCapabilities = mockCapabilities as any;

      component['saveData']();

      expect(mockRoleService.updateRole).toHaveBeenCalledWith(
        expect.arrayContaining([
          expect.objectContaining({
            capabilities: mockCapabilities
          })
        ])
      );
    });
  });

  describe('Editing State Integration', () => {
    it('should handle EDIT action', () => {
      component.rowData = mockRoles;
      const editingStateSubject = new Subject<FormAction>();
      mockEditingStateService.getValue.mockReturnValue(editingStateSubject.asObservable());

      // Re-create component to trigger constructor subscription
      fixture = TestBed.createComponent(RoleListComponent);
      component = fixture.componentInstance;
      component.rowData = mockRoles;

      editingStateSubject.next(FormAction.EDIT);

      expect(mockEditingStateService.setData).toHaveBeenCalledWith([
        ['rowData', JSON.parse(JSON.stringify(mockRoles))]
      ]);
    });

    it('should handle SAVE action', () => {
      jest.spyOn(component as any, 'saveData').mockImplementation(() => {});
      const editingStateSubject = new Subject<FormAction>();
      mockEditingStateService.getValue.mockReturnValue(editingStateSubject.asObservable());

      // Re-create component to trigger constructor subscription
      fixture = TestBed.createComponent(RoleListComponent);
      component = fixture.componentInstance;

      editingStateSubject.next(FormAction.SAVE);

      expect(component['saveData']).toHaveBeenCalled();
    });

    it('should handle SUBMIT action', () => {
      const editingStateSubject = new Subject<FormAction>();
      mockEditingStateService.getValue.mockReturnValue(editingStateSubject.asObservable());

      // Re-create component to trigger constructor subscription
      fixture = TestBed.createComponent(RoleListComponent);
      component = fixture.componentInstance;

      editingStateSubject.next(FormAction.SUBMIT);

      expect(mockRouter.navigateByUrl).toHaveBeenCalledWith('/');
      expect(mockRouter.navigate).toHaveBeenCalledWith(['admin', 'role-list']);
    });
  });

  describe('Component Lifecycle', () => {
    it('should complete destroy$ subject on ngOnDestroy', () => {
      const destroySpy = jest.spyOn(component['destroy$'], 'next');
      const completeSpy = jest.spyOn(component['destroy$'], 'complete');

      component.ngOnDestroy();

      expect(destroySpy).toHaveBeenCalled();
      expect(completeSpy).toHaveBeenCalled();
    });

    it('should unsubscribe from observables on destroy', () => {
      const destroySpy = jest.spyOn(component['destroy$'], 'next');

      component.ngOnDestroy();

      expect(destroySpy).toHaveBeenCalled();
    });
  });

  describe('Edge Cases', () => {
    it('should handle empty rowData', () => {
      component.rowData = [];

      expect(() => {
        component.loadTree();
      }).not.toThrow();

      expect(component.nodes).toEqual([]);
      expect(component.links).toEqual([]);
    });

    it('should handle roles without superiors', () => {
      const rolesWithoutSuperiors = [
        { ...mockRoles[0], superiorId: null, superior: null }
      ];
      component.rowData = rolesWithoutSuperiors;

      component.loadTree();

      expect(component.nodes.length).toBe(1);
      expect(component.links.length).toBe(0);
    });

    it('should handle grid API redraw when available', () => {
      const mockGridApi = { redrawRows: jest.fn() };
      component.gridApi = mockGridApi as any;

      const editingStateSubject = new Subject<FormAction>();
      mockEditingStateService.getValue.mockReturnValue(editingStateSubject.asObservable());

      // Re-create component to trigger constructor subscription
      fixture = TestBed.createComponent(RoleListComponent);
      component = fixture.componentInstance;
      component.gridApi = mockGridApi as any;

      editingStateSubject.next(FormAction.EDIT);

      expect(mockGridApi.redrawRows).toHaveBeenCalled();
    });
  });
});
