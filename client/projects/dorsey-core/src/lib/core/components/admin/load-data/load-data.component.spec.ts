import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ActivatedRoute } from '@angular/router';
import { of, throwError } from 'rxjs';
import { GridApi } from 'ag-grid-community';

import { LoadDataComponent } from './load-data.component';
import { UploadedDataService } from '../../../services/admin/uploaded-data.service';
import { UploadSummaryEnum } from './upload-summary.enum';
import { StatusCellRendererBackgroundColor } from './status-cell-renderer/status-cell-renderer.component';
import { IUploadedData } from '../../../models/admin/system/uploaded-data.model';

describe('LoadDataComponent', () => {
  let component: LoadDataComponent;
  let fixture: ComponentFixture<LoadDataComponent>;
  let mockUploadedDataService: jest.Mocked<UploadedDataService>;
  let mockActivatedRoute: jest.Mocked<ActivatedRoute>;

  const mockUploadedData: IUploadedData[] = [
    {
      dataSet: 'Test Dataset 1',
      dataSetCode: 'TD1',
      status: UploadSummaryEnum.LOCKED,
      uploadedDate: new Date('2023-01-01'),
      displayOrder: 2,
      fileName: 'test1.xlsx'
    },
    {
      dataSet: 'Test Dataset 2',
      dataSetCode: 'TD2',
      status: UploadSummaryEnum.UPLOADED,
      uploadedDate: new Date('2023-01-02'),
      displayOrder: 1,
      fileName: 'test2.xlsx'
    },
    {
      dataSet: 'Test Dataset 3',
      dataSetCode: 'TD3',
      status: UploadSummaryEnum.NO_DATA,
      uploadedDate: new Date('2023-01-03'),
      displayOrder: 3,
      fileName: 'test3.xlsx'
    }
  ];

  beforeEach(async () => {
    mockUploadedDataService = {
      findAll: jest.fn().mockReturnValue(of(mockUploadedData))
    } as any;

    mockActivatedRoute = {
      snapshot: { params: {}, queryParams: {} },
      params: of({}),
      queryParams: of({})
    } as any;

    await TestBed.configureTestingModule({
      declarations: [LoadDataComponent],
      providers: [
        { provide: UploadedDataService, useValue: mockUploadedDataService },
        { provide: ActivatedRoute, useValue: mockActivatedRoute }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(LoadDataComponent);
    component = fixture.componentInstance;
  });

  describe('Component Initialization', () => {
    it('should create', () => {
      expect(component).toBeTruthy();
    });

    it('should initialize with default values', () => {
      expect(component.rowData).toEqual([]);
      expect(component.gridApi).toBeUndefined();
      expect(component.columnDefs).toBeDefined();
      expect(component.columnDefs.length).toBe(3);
    });

    it('should have correct column definitions', () => {
      const columns = component.columnDefs;

      expect(columns[0].field).toBe('dataSet');
      expect(columns[0].headerName).toBe('Data Set Name');
      expect(columns[1].field).toBe('status');
      expect(columns[2].field).toBe('uploadedDate');
      expect(columns[2].headerName).toBe('Status Change Dt');
    });
  });

  describe('ngOnInit', () => {
    it('should call loadData on initialization', () => {
      jest.spyOn(component as any, 'loadData');

      component.ngOnInit();

      expect(component['loadData']).toHaveBeenCalled();
    });

    it('should load data and sort by displayOrder', () => {
      fixture.detectChanges(); // This triggers ngOnInit

      expect(mockUploadedDataService.findAll).toHaveBeenCalled();
      // Data should be sorted by displayOrder: 1, 2, 3
      expect(component.rowData.length).toBe(3);
      expect(component.rowData[0].displayOrder).toBe(1);
      expect(component.rowData[1].displayOrder).toBe(2);
      expect(component.rowData[2].displayOrder).toBe(3);
    });
  });

  describe('Data Loading', () => {
    it('should handle successful data loading', () => {
      fixture.detectChanges();

      expect(component.rowData.length).toBe(3);
      expect(component.rowData[0].displayOrder).toBe(1);
      expect(component.rowData[1].displayOrder).toBe(2);
      expect(component.rowData[2].displayOrder).toBe(3);
    });

    it('should handle empty data response', () => {
      mockUploadedDataService.findAll.mockReturnValue(of([]));

      fixture.detectChanges();

      expect(component.rowData).toEqual([]);
    });

    it('should handle service error gracefully', () => {
      mockUploadedDataService.findAll.mockReturnValue(throwError('Service error'));

      expect(() => {
        fixture.detectChanges();
      }).not.toThrow();
    });
  });

  describe('Status Cell Renderer Color Selection', () => {
    it('should return GREEN for LOCKED status', () => {
      const result = component.statusCellRendererColorSelection(UploadSummaryEnum.LOCKED);
      expect(result).toBe(StatusCellRendererBackgroundColor.GREEN);
    });

    it('should return YELLOW for UPLOADED status', () => {
      const result = component.statusCellRendererColorSelection(UploadSummaryEnum.UPLOADED);
      expect(result).toBe(StatusCellRendererBackgroundColor.YELLOW);
    });

    it('should return RED for NO_DATA status', () => {
      const result = component.statusCellRendererColorSelection(UploadSummaryEnum.NO_DATA);
      expect(result).toBe(StatusCellRendererBackgroundColor.RED);
    });

    it('should return RED for undefined status (default case)', () => {
      const result = component.statusCellRendererColorSelection(undefined as any);
      expect(result).toBe(StatusCellRendererBackgroundColor.RED);
    });
  });

  describe('Column Configuration', () => {
    it('should configure dataSet column with hyperlink renderer', () => {
      const dataSetColumn = component.columnDefs[0];

      expect(dataSetColumn.field).toBe('dataSet');
      expect(dataSetColumn.headerName).toBe('Data Set Name');
      expect(dataSetColumn.cellRenderer).toBeDefined();
    });

    it('should configure status column with status renderer', () => {
      const statusColumn = component.columnDefs[1];

      expect(statusColumn.field).toBe('status');
      expect(statusColumn.cellClass).toBe('text-center');
      expect(statusColumn.cellRenderer).toBeDefined();
      expect(statusColumn.cellRendererParams).toBeDefined();
      expect((statusColumn.cellRendererParams as any).colorSelectionFunction).toBe(component.statusCellRendererColorSelection);
    });

    it('should configure uploadedDate column with date formatter', () => {
      const dateColumn = component.columnDefs[2];

      expect(dateColumn.field).toBe('uploadedDate');
      expect(dateColumn.headerName).toBe('Status Change Dt');
      expect(dateColumn.cellClass).toBe('text-center');
      expect(dateColumn.valueFormatter).toBeDefined();
    });

    it('should format date values correctly', () => {
      const dateColumn = component.columnDefs[2];
      const testDate = new Date('2023-01-15T14:30:00');

      const formattedDate = dateColumn.valueFormatter({ value: testDate });
      expect(formattedDate).toBe('01/15/2023 14:30');
    });

    it('should handle null date values', () => {
      const dateColumn = component.columnDefs[2];

      const formattedDate = dateColumn.valueFormatter({ value: null });
      expect(formattedDate).toBeNull();
    });
  });

  describe('Grid API Integration', () => {
    it('should handle grid API assignment', () => {
      const mockGridApi = { test: 'api' } as any;

      component.gridApi = mockGridApi;

      expect(component.gridApi).toBe(mockGridApi);
    });
  });

  describe('Component Lifecycle', () => {
    it('should complete destroy$ subject on ngOnDestroy', () => {
      const destroySpy = jest.spyOn(component['destroy$'], 'next');
      const completeSpy = jest.spyOn(component['destroy$'], 'complete');

      component.ngOnDestroy();

      expect(destroySpy).toHaveBeenCalled();
      expect(completeSpy).toHaveBeenCalled();
    });

    it('should unsubscribe from observables on destroy', () => {
      fixture.detectChanges(); // Start subscription

      const destroySpy = jest.spyOn(component['destroy$'], 'next');

      component.ngOnDestroy();

      expect(destroySpy).toHaveBeenCalled();
    });
  });

  describe('Cell Renderer Parameters', () => {
    it('should provide correct parameters for hyperlink cell renderer', () => {
      const dataSetColumn = component.columnDefs[0];
      const mockParams = {
        api: {
          getDisplayedRowAtIndex: jest.fn().mockReturnValue({
            data: { dataSetCode: 'TD1', dataSet: 'Test Dataset' }
          })
        },
        rowIndex: 0
      };

      const result = (dataSetColumn.cellRendererParams as any)(mockParams);

      expect(result.route).toBe('./upload-detail');
      expect(result.queryParams).toEqual({ dataSetCode: 'TD1', dataSet: 'Test Dataset' });
    });
  });

  describe('Data Sorting', () => {
    it('should sort data by displayOrder in ascending order', () => {
      const unsortedData = [
        { ...mockUploadedData[0], displayOrder: 3 },
        { ...mockUploadedData[1], displayOrder: 1 },
        { ...mockUploadedData[2], displayOrder: 2 }
      ];

      mockUploadedDataService.findAll.mockReturnValue(of(unsortedData));

      fixture.detectChanges();

      expect(component.rowData[0].displayOrder).toBe(1);
      expect(component.rowData[1].displayOrder).toBe(2);
      expect(component.rowData[2].displayOrder).toBe(3);
    });
  });
});
