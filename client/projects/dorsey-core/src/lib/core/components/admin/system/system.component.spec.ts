import { ComponentFixture, TestBed } from '@angular/core/testing';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';

import { SystemComponent } from './system.component';
import { EditingStateService } from '../../../services/editing-state.service';
import { RoleActions } from '../../../models/enums/role-actions';

describe('SystemComponent', () => {
  let component: SystemComponent;
  let fixture: ComponentFixture<SystemComponent>;
  let mockEditingStateService: jest.Mocked<EditingStateService>;

  beforeEach(async () => {
    mockEditingStateService = {
      editingState: {
        isEditing: false,
        hasChanges: false
      }
    } as any;

    await TestBed.configureTestingModule({
      declarations: [SystemComponent],
      providers: [
        { provide: EditingStateService, useValue: mockEditingStateService }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();

    fixture = TestBed.createComponent(SystemComponent);
    component = fixture.componentInstance;
  });

  describe('Component Initialization', () => {
    it('should create', () => {
      expect(component).toBeTruthy();
    });

    it('should initialize with default values', () => {
      expect(component.activeIndex).toBe(0);
      expect(component.roleAction).toBe(RoleActions);
      expect(component.amount).toBe(0.5);
    });

    it('should inject EditingStateService', () => {
      expect(component.editingStateService).toBe(mockEditingStateService);
    });
  });

  describe('ngOnInit', () => {
    it('should call ngOnInit without errors', () => {
      expect(() => {
        component.ngOnInit();
      }).not.toThrow();
    });

    it('should not modify component state in ngOnInit', () => {
      const initialActiveIndex = component.activeIndex;
      const initialAmount = component.amount;

      component.ngOnInit();

      expect(component.activeIndex).toBe(initialActiveIndex);
      expect(component.amount).toBe(initialAmount);
    });
  });

  describe('onSwitch', () => {
    it('should handle onSwitch event without errors', () => {
      const mockEvent = { index: 1 };

      expect(() => {
        component.onSwitch(mockEvent);
      }).not.toThrow();
    });

    it('should accept various event types', () => {
      expect(() => {
        component.onSwitch(null);
      }).not.toThrow();

      expect(() => {
        component.onSwitch(undefined);
      }).not.toThrow();

      expect(() => {
        component.onSwitch({ index: 0 });
      }).not.toThrow();

      expect(() => {
        component.onSwitch({ someOtherProperty: 'value' });
      }).not.toThrow();
    });
  });

  describe('Properties', () => {
    it('should have correct activeIndex property', () => {
      expect(typeof component.activeIndex).toBe('number');
      expect(component.activeIndex).toBe(0);
    });

    it('should have roleAction property set to RoleActions enum', () => {
      expect(component.roleAction).toBe(RoleActions);
    });

    it('should have amount property as number', () => {
      expect(typeof component.amount).toBe('number');
      expect(component.amount).toBe(0.5);
    });

    it('should allow activeIndex to be modified', () => {
      component.activeIndex = 2;
      expect(component.activeIndex).toBe(2);
    });

    it('should allow amount to be modified', () => {
      component.amount = 1.0;
      expect(component.amount).toBe(1.0);
    });
  });

  describe('EditingStateService Integration', () => {
    it('should access editing state through service', () => {
      expect(component.editingStateService.editingState.isEditing).toBe(false);
      expect(component.editingStateService.editingState.hasChanges).toBe(false);
    });

    it('should react to editing state changes', () => {
      mockEditingStateService.editingState.isEditing = true;
      mockEditingStateService.editingState.hasChanges = true;

      expect(component.editingStateService.editingState.isEditing).toBe(true);
      expect(component.editingStateService.editingState.hasChanges).toBe(true);
    });
  });

  describe('Component Template Integration', () => {
    it('should render without errors', () => {
      expect(() => {
        fixture.detectChanges();
      }).not.toThrow();
    });

    it('should maintain component state after template rendering', () => {
      fixture.detectChanges();

      expect(component.activeIndex).toBe(0);
      expect(component.amount).toBe(0.5);
      expect(component.roleAction).toBe(RoleActions);
    });
  });

  describe('Edge Cases', () => {
    it('should handle negative activeIndex', () => {
      component.activeIndex = -1;
      expect(component.activeIndex).toBe(-1);
    });

    it('should handle large activeIndex values', () => {
      component.activeIndex = 999;
      expect(component.activeIndex).toBe(999);
    });

    it('should handle zero amount', () => {
      component.amount = 0;
      expect(component.amount).toBe(0);
    });

    it('should handle negative amount', () => {
      component.amount = -0.5;
      expect(component.amount).toBe(-0.5);
    });
  });
});
