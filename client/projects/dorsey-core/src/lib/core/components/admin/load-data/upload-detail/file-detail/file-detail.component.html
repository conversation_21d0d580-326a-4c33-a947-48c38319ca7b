<div class="d-flex flex-column p-3 h-100">
  <p class="title m-0">File Details - {{ uploadedData?.fileName }}</p>
  <div class="text-end mb-2">
    <div class="action-button-container">
      <p-button label="Download" *ngIf='file' icon="pi pi-download" iconPos="left" (onClick)="onDownload()"></p-button>
    </div>
  </div>
  <p-panel header='Details' class="detail-panel">
    <div class="row pt-2 p-0 m-0">
      <div class='col-12 m-0 p-0'>
        <p-tabView styleClass="tabview-custom">
          <p-tabPanel *ngFor="let sheet of workbook?.SheetNames">
            <ng-template pTemplate="header">
              <span>{{sheet}}</span>
            </ng-template>
            <datagrid
              [id]="sheet"
              [columns]="loadColumnDef(workbook.Sheets[sheet])"
              [rows]="loadGridData(workbook.Sheets[sheet])"
              [showPagination]="true"
              [showExportCta]='false'
            >
            </datagrid>
          </p-tabPanel>
        </p-tabView>
      </div>
    </div>
  </p-panel>

</div>

