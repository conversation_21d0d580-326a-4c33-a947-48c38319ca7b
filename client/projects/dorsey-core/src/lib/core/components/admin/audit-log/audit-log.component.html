<div class="d-flex flex-column p-3 h-100">
  <p-panel>
    <ng-template pTemplate="header">
      <div class="row col-12 m-0">
        <p class="p-0">To view entries in the audit log, select the report criteria and click Run.</p>
        <div class="row col-11 p-0 text-end apply-gap">
          <div class="col-3 row p-0 align-items-center">
            <div class="col-6 text-end">User</div>
            <div class="col-6 p-0">
              <select class="form-select w-100" [(ngModel)]="userEmail" id="year" name="year">
                <option value="All">All</option>
                <option *ngFor="let userEmail of filter?.userEmails" [value]="userEmail">
                  {{ userEmail }}
                </option>
              </select>
            </div>
          </div>
          <div class="col-3 row p-0 align-items-center">
            <div class="col-6">Action Taken</div>
            <div class="col-6 p-0">
              <select class="form-select w-100" [(ngModel)]="action" id="action" name="year">
                <option value="All">All</option>
                <option *ngFor="let action of filter?.actions" [value]="action">
                  {{ action }}
                </option>
              </select>
            </div>
          </div>
          <div class="col-3 row p-0 align-items-center">
            <div class="col-6">Entity Type</div>
            <div class="col-6 p-0">
              <select class="form-select w-100" [(ngModel)]="entityType" id="entityType" name="year">
                <option value="All">All</option>
                <option *ngFor="let entityType of filter?.entityTypes" [value]="entityType">
                  {{ entityType }}
                </option>
              </select>
            </div>
          </div>
          <div class="col-3 row p-0 align-items-center">
            <div class="col-6">Start Date</div>
            <div class="col-6 p-0">
              <p-calendar
                id="startDate"
                [showIcon]="true"
                [dataType]="'string'"
                [(ngModel)]="startDate"
                appendTo="body"
              ></p-calendar>
            </div>
          </div>
          <div class="col-3 row p-0 align-items-center">
            <div class="col-6">End Date</div>
            <div class="col-6 p-0">
              <p-calendar
                id="endDate"
                [showIcon]="true"
                [dataType]="'string'"
                [(ngModel)]="endDate"
                appendTo="body"
              ></p-calendar>
            </div>
          </div>
        </div>
        <div class="row col-12 p-0 text-end">
          <div>
            <p-button
              label="Run"
              icon="fa fa-play"
              iconPos="left"
              class="primary-button me-3"
              placement="left"
              (onClick)="onRun()"
            ></p-button>
          </div>
        </div>
      </div>
    </ng-template>
    <div>
      <div>
        <datagrid
          #auditLogList
          id="auditLogGrid"
          [name]="'Audit Log'"
          class="ag-theme-alpine"
          (gridIsReady)="onGridIsReady($event)"
          [rows]="auditLogs"
          [columns]="columnDefs"
          [searchValue]="searchValue"
          [showPagination]="true"
          [pageSize]="gridPageSize"
          [hasAutoHeight]="false"
          (cellAction)="onCellAction($event)"
          [showActions]="auditLogList.ACTION_STATE.VIEW"
        >
        </datagrid>
      </div>
    </div>
  </p-panel>

  <p-dialog
    appendTo="body"
    header="Audit Details"
    [(visible)]="displayDialog"
    [modal]="true"
    [style]="{ width: prevAuditLog ? '1000px' : '500px' }"
    *ngIf="auditLog"
  >
    <div [ngSwitch]="auditLog?.actionCode">
      <div *ngSwitchCase="'Delete'">
        <p-panel header="Values Captured Before Delete Performed">
          <div class="card-body">
            <div *ngFor="let item of getEntityValue(auditLog) | keyvalue">
              <div class="row mb-1">
                <div class="col-6 text-end">
                  <label [for]="item.key">
                    {{ item.key }}
                  </label>
                </div>
                <div class="col-6">
                  {{ item.value | logObjectPipe | json }}
                </div>
              </div>
            </div>
          </div>
        </p-panel>
      </div>
      <div *ngSwitchCase="'Update'">
        <div class="row">
          <div class="col-6">
            <p-panel
              *ngIf="prevAuditLog && prevAuditLog.auditDate !== auditLog.auditDate"
              header="Values Captured Before Update Performed"
              class="detail-panel"
            >
              <div class="card-body">
                <div *ngFor="let item of getEntityValue(prevAuditLog) | keyvalue">
                  <div class="row mb-1">
                    <div class="col-6 text-end">
                      <label [for]="item.key">
                        {{ item.key }}
                      </label>
                    </div>
                    <div class="col-6">
                      {{ item.value | logObjectPipe | json }}
                    </div>
                  </div>
                </div>
              </div>
            </p-panel>
          </div>
          <div [class]="prevAuditLog ? 'col-6' : 'col-12'">
            <p-panel header="Values Captured After Update Performed" class="detail-panel">
              <div class="card-body">
                <div *ngFor="let item of getEntityValue(auditLog) | keyvalue">
                  <div class="row mb-1">
                    <div class="col-6 text-end">
                      <label [for]="item.key">
                        {{ item.key }}
                      </label>
                    </div>
                    <div class="col-6">
                      {{ item.value | logObjectPipe | json }}
                    </div>
                  </div>
                </div>
              </div>
            </p-panel>
          </div>
        </div>
      </div>
      <div *ngSwitchCase="'Upload'">
        <div class="row mb-1 text-nowrap">
          <div class="col-5 text-end">
            <label> File Uploaded </label>
          </div>
          <div class="col-7">
            <a
              *ngIf="isExcel(getEntityValue(auditLog)?.fileName)"
              [routerLink]="'../upload-data-summary/file-detail'"
              [state]="getEntityValue(auditLog)"
              >{{ getEntityValue(auditLog)?.fileName }}</a
            >
            <span *ngIf="!isExcel(getEntityValue(auditLog)?.fileName)">{{ getEntityValue(auditLog)?.fileName }}</span>
          </div>
        </div>
      </div>
    </div>

    <ng-template pTemplate="footer">
      <p-button type="button" data-dismiss="modal" (onClick)="auditLog = null">close</p-button>
    </ng-template>
  </p-dialog>
</div>
