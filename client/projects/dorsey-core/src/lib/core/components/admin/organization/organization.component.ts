import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewChild } from '@angular/core';

import { ColDef, GridApi } from 'ag-grid-community';
import { takeUntil } from 'rxjs/operators';
import { firstValueFrom, Subject } from 'rxjs';

import { Router } from '@angular/router';
import { DatagridComponent } from '../../datagrid/datagrid.component';
import { GridRow } from '../../datagrid/models/grid-row.model';
import { IHierarchyLevel } from '../../../models/admin/organization/hierarchy-level';
import { IHierarchyNode } from '../../../models/admin/organization/hierarchy-node';
import { RoleActions } from '../../../models/enums/role-actions';
import { EditingStateService } from '../../../services/editing-state.service';
import { OrganizationService } from '../../../services/admin/organization.service';
import { ToastService } from '../../../services/toast.service';
import { DialogMessageService } from '../../../services/dialog-message.service';
import { FormAction } from '../../../models/form-action';
import { getGridData, handleCancelAction } from '../../../utils/grid-utils';
import { CheckboxRendererComponent } from '../../datagrid/customs/cell-renderers/checkbox-renderer/checkbox-renderer.component';
import { ActionState } from '../../datagrid/models/action-state';
import { DataGridMessage } from '../../datagrid/models/datagrid-message.model';
import { DatagridActionsCta } from '../../datagrid/models/enums/datagrid-actions-cta';

@Component({
  selector: 'dorsey-organization',
  templateUrl: './organization.component.html',
  styleUrls: ['./organization.component.scss'],
})
export class OrganizationComponent implements OnInit, OnDestroy {
  @ViewChild('hierarchyTree') hierarchyTreeGrid: DatagridComponent;

  private readonly destroy$ = new Subject<void>();

  levelColumnDefs: any[];
  levelRowData: GridRow<IHierarchyLevel>[];
  levelGridApi: GridApi;

  columnDefs: ColDef[];
  autoGroupColumnDef: ColDef;
  rowData: GridRow<IHierarchyNode>[];

  defRowData: any[] = [];
  gridApi: GridApi;

  path = location.pathname;
  actions = RoleActions;

  selectedTab = 0;
  levelErrors: string[] = [];
  nodeErrors: string[] = [];

  constructor(
    public editingStateService: EditingStateService,
    private organizationHierarchyService: OrganizationService,
    private toastService: ToastService,
    private dialogMessageService: DialogMessageService,
    private router: Router
  ) {
    this.editingStateService
      .getValue()
      .pipe(takeUntil(this.destroy$))
      .subscribe((value) => {
        switch (value) {
          case FormAction.EDIT:
            this.editingStateService.setData([
              ['levelRowData', JSON.parse(JSON.stringify(this.levelRowData))],
            ]);
            this.editingStateService.setData([
              ['rowData', JSON.parse(JSON.stringify(this.rowData))],
            ]);
            break;
          case FormAction.SAVE:
            if (this.selectedTab === 1) {
              this.gridApi.stopEditing();
              this.saveNodes();
            } else {
              this.levelGridApi.stopEditing();
              this.saveLevels();
            }
            break;
        }
        this.levelGridApi?.redrawRows();
      });
    firstValueFrom(handleCancelAction(this.editingStateService, this));
  }

  ngOnInit() {
    this.setLevelColumnDefinition();
    this.loadLevels();
    this.setColumnDefinition();
  }

  setLevelColumnDefinition() {
    this.levelColumnDefs = [
      {
        field: 'level',
        minWidth: 100,
        maxWidth: 100,
      },
      {
        field: 'inUse',
        minWidth: 100,
        maxWidth: 100,
        cellRenderer: CheckboxRendererComponent,
        cellRendererParams: () => {
          return {
            disabled: true,
          };
        },
      },
      {
        field: 'hierarchyName',
        editable: true,
        minWidth: 125,
        cellEditor: 'agTextCellEditor',
        cellEditorParams: {
          maxLength: 25,
        },
      },
      {
        field: 'description',
        editable: true,
        minWidth: 500,
        cellEditor: 'agTextCellEditor',
        cellEditorParams: {
          maxLength: 255,
        },
      },
    ];
  }

  onLevelNewRow(): any {
    const next = this.levelRowData.length;
    return {
      level: next,
      hierarchyName: '',
      description: '',
    };
  }

  private saveLevels() {
    if (this.validateLevelData()) {
      this.organizationHierarchyService
        .updateLevels(this.levelRowData)
        .pipe(takeUntil(this.destroy$))
        .subscribe(
          () => {
            this.editingStateService.setValue(FormAction.SUBMIT);
            this.toastService.displaySuccess(
              'Hierarchy Levels saved successfully.'
            );
            this.loadLevels();
          },
          () =>
            this.toastService.displayError(
              'Failed while saving Hierarchy Levels data.'
            )
        );
    }
  }

  private saveNodes() {
    if (this.validateData()) {
      this.organizationHierarchyService
        .updateNodes(this.rowData)
        .pipe(takeUntil(this.destroy$))
        .subscribe(
          () => {
            this.toastService.displaySuccess(
              'Hierarchy Tree saved successfully.'
            );
            this.editingStateService.setValue(FormAction.SUBMIT);
            this.loadLevels();
          },
          () =>
            this.toastService.displayError(
              'Failed while saving Hierarchy Tree data.'
            )
        );
    }
  }

  private loadLevels() {
    this.organizationHierarchyService.findLevels().subscribe(
      (resp) => {
        resp.forEach(
          (v) =>
            (v.action = v.inUse
              ? ActionState.ADD_EDIT
              : ActionState.ADD_EDIT_DELETE)
        );
        this.levelRowData = resp.sort((a, b) => a.level - b.level);
        this.loadNodes();
      },
      () =>
        this.toastService.displayError('Failed loading Organization Levels.')
    );
  }

  private loadNodes() {
    this.organizationHierarchyService.findOrgHierarchy().subscribe((resp) => {
      resp.forEach((v) => {
        if (v.nodeId.length < this.levelRowData.length) {
          if (!v.canDelete) {
            v.action = ActionState.TREE_ADD_EDIT;
          } else {
            v.action = ActionState.TREE_ADD_EDIT_DELETE;
          }
        } else {
          if (!v.canDelete) {
            v.action = ActionState.EDIT;
          } else {
            v.action = ActionState.EDIT_DELETE;
          }
        }
      });
      this.rowData = resp;
    });
  }

  onLevelGridIsReady(gridApi: GridApi) {
    this.levelGridApi = gridApi;
  }

  private setColumnDefinition() {
    this.columnDefs = [
      {
        field: 'hierarchyValue',
        editable: true,
        minWidth: 125,
        cellEditor: 'agTextCellEditor',
        cellEditorParams: {
          maxLength: 25,
        },
      },
      {
        field: 'users',
        headerName: 'Associated Users',
        editable: false,
        minWidth: 125,
        maxWidth: 125,
      },
      {
        field: 'items',
        headerName: 'Associated Jobs',
        editable: false,
        minWidth: 125,
        maxWidth: 125,
      },
    ];

    this.autoGroupColumnDef = {
      field: 'nodeId',
      headerName: 'Hierarchy Level',
      minWidth: 200,
      editable: false,
      cellClass: 'not-editable',
      valueFormatter: (params) => {
        return this.levelRowData[params?.data?.nodeId.length - 1]
          ?.hierarchyName;
      },
      cellRendererParams: {
        suppressCount: true,
      },
    };
  }

  getDataPath: any = (data: any) => {
    return data.nodeId;
  };

  onGridIsReady(gridApi: GridApi) {
    this.gridApi = gridApi;
  }

  onCellAction(message: DataGridMessage) {
    switch (message.action) {
      case DatagridActionsCta.ADD:
        this.addChildNode(message);
        break;
      case DatagridActionsCta.DELETE:
        this.deleteNode(message);
        break;
    }
  }

  deleteNode(message: DataGridMessage) {
    this.rowData = this.rowData.filter((r) => {
      return (
        r.nodeId[message.rawData.nodeId.length - 1] !==
        message.rawData.nodeId[message.rawData.nodeId.length - 1]
      );
    });
    this.gridApi.setRowData(this.rowData);
  }

  private validateLevelData(): boolean {
    const data = getGridData(this.levelGridApi);
    for (const row of data) {
      if (!row.hierarchyName) {
        this.levelErrors.push(
          `${(
            this.levelErrors.length + 1
          ).toString()}. Hierarchy Levels Grid: Row ${
            row.rowId + 1
          }: Hierarchy Name is required.`
        );
      }
      if (
        data.filter(
          (r) =>
            r.hierarchyName?.length &&
            r.hierarchyName.trim().toLowerCase() ===
              row.hierarchyName.trim().toLowerCase()
        ).length > 1
      ) {
        this.levelErrors.push(
          `${(
            this.levelErrors.length + 1
          ).toString()}. Hierarchy Levels Grid: Row ${
            row.rowId + 1
          }: Hierarchy Name is duplicated.`
        );
      }
    }

    if (this.levelErrors.length) {
      this.dialogMessageService.displayError(
        this.levelErrors.join('\n'),
        false
      );
      this.levelErrors = [];
      return false;
    }

    return !this.levelErrors.length;
  }

  private validateData(): boolean {
    const data = getGridData(this.gridApi);
    for (const row of data) {
      if (
        data
          .filter((n) => n.nodeId.length === row.nodeId.length)
          .filter(
            (r) =>
              r.hierarchyValue?.length &&
              r.hierarchyValue.trim().toLowerCase() ===
                row.hierarchyValue.trim().toLowerCase()
          ).length > 1
      ) {
        this.nodeErrors.push(
          `${(
            this.nodeErrors.length + 1
          ).toString()}. Organization Tree Grid: Row ${
            row.rowId + 1
          }: Hierarchy Value is duplicated.`
        );
      }
      if (!row.hierarchyValue) {
        this.nodeErrors.push(
          `${(
            this.nodeErrors.length + 1
          ).toString()}. Organization Tree Grid: Row ${
            row.rowId + 1
          }: Hierarchy Value is required.`
        );
      }
    }

    if (this.nodeErrors.length) {
      this.dialogMessageService.displayError(this.nodeErrors.join('\n'), false);
      this.nodeErrors = [];
      return false;
    }

    return !this.nodeErrors.length;
  }

  private addChildNode(message: DataGridMessage) {
    let currLevel = Math.max(
      ...this.rowData
        .filter((r) => r.nodeId.length === message.rawData.nodeId.length + 1)
        .map((n) => n.nodeId[n.nodeId.length - 1])
    );

    const nextLevel = [
      ...message.rawData.nodeId,
      ...[isFinite(currLevel) ? ++currLevel : 1],
    ];

    this.rowData.push({
      action:
        message.rawData.nodeId.length + 1 < this.levelRowData.length
          ? ActionState.TREE_ADD_EDIT_DELETE
          : ActionState.ADD_EDIT_DELETE,
      isEditing: true,
      isDisabled: false,
      rowId: this.hierarchyTreeGrid.rowId++,
      nodeId: nextLevel,
      parentId: [...message.rawData.nodeId],
      hierarchyValue: '',
      users: 0,
      items: 0,
      canDelete: true,
    });

    this.gridApi.setRowData(this.rowData);
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
