import { Route, Router } from '@angular/router';
import { IRoleCapabilities } from '../../../../models/admin/roles/role-capabilities';

function buildCapabilitiesTree(
  router: Router
): [IRoleCapabilities[], Map<string, string>] {
  let data: IRoleCapabilities[] = [];
  let pathTitleMap = new Map<string, string>();

  router.config
    .filter((r) => !r.loadChildren)
    .forEach((r) => {
      gridDataBuilder(data, pathTitleMap, r, []);
    });

  return [data, pathTitleMap];
}

function gridDataBuilder(
  data: IRoleCapabilities[],
  pathTitleMap: Map<string, string>,
  route: Route,
  path: string[]
) {
  if (route.title) {
    path.push(!route.path ? route.data['parentPath'] : route.path);

    data.push({
      component: [...path],
      view: false,
      edit: false,
      create: false,
      delete: false,
    });

    pathTitleMap.set(path.join(','), <string>route.title);
  }

  for (const child of route.children ?? []) {
    gridDataBuilder(data, pathTitleMap, child, [...path]);
  }
}

export default buildCapabilitiesTree;
