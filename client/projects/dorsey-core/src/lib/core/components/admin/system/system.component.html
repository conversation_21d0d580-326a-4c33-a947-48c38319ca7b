<div class="d-flex flex-column p-3 h-100">
  <div class="text-end mb-2">
    <div class="action-button-container">
      <dorsey-edition-ctas *hasAnyRole="roleAction.EDIT;path:'/admin/system'" class="col-3 p-0 text-end"
                           [canEdit]="true" [editionDisabled]="false">
      </dorsey-edition-ctas>
    </div>
  </div>
  <div>
    <p-tabView styleClass="tabview-custom" [(activeIndex)]="activeIndex" (onChange)="onSwitch($event)">
      <p-tabPanel [disabled]="editingStateService.editingState.isEditing">
        <ng-template pTemplate="header">
          <i class="pi pi-table me-1"></i>
          <span>Appearance</span>
        </ng-template>
        <dorsey-appearance *ngIf="activeIndex === 0"></dorsey-appearance>
      </p-tabPanel>
      <p-tabPanel>
        <ng-template pTemplate="header">
          <i class="pi pi-dollar me-1"></i>
          <span>Payment Gateway (Demo)</span>
        </ng-template>
        <div class="ms-1 mt-5 d-flex">
          <p-inputNumber
            [(ngModel)]="amount"
            [min]="0.50"
            class="me-3"
            inputId="currency-us"
            mode="currency"
            currency="USD"
            locale="en-US" />
          <button class="btn btn-danger" appPaymentDialog [amount]="amount">Purchase</button>
        </div>
      </p-tabPanel>
    </p-tabView>
  </div>
</div>
