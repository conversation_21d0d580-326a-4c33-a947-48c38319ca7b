import { Component, OnInit } from '@angular/core';

import { RoleActions } from '../../../models/enums/role-actions';
import { EditingStateService } from '../../../services/editing-state.service';

@Component({
  selector: 'dorsey-system',
  templateUrl: './system.component.html',
  styleUrls: ['./system.component.scss'],
})
export class SystemComponent implements OnInit {
  activeIndex = 0;
  roleAction = RoleActions;
  amount = 0.5;

  constructor(public editingStateService: EditingStateService) {}

  ngOnInit(): void {}

  onSwitch(event) {}
}
