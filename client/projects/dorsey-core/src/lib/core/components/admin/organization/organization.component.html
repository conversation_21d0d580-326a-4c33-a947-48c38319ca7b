<div class="d-flex flex-column p-3 h-100">
    <div class="text-end mb-2">
        <div class="action-button-container">
            <dorsey-edition-ctas *hasAnyRole="actions.EDIT;path:path" class="col-3 p-0 text-end" [canEdit]="true" [editionDisabled]="!rowData">
            </dorsey-edition-ctas>
        </div>
    </div>
    <div>
        <p-tabView styleClass="tabview-custom" [(activeIndex)]="selectedTab">
            <p-tabPanel [disabled]="editingStateService.editingState.isEditing">
                <ng-template pTemplate="header">
                    <i class="pi pi-table me-1"></i>
                    <span>Hierarchy Levels</span>
                </ng-template>
                <div class="w-75">
                    <datagrid
                            *ngIf="levelRowData"
                            #hierarchyLevels
                            id="hierarchy-levels"
                            [name]="'Hierarchy Levels'"
                            [columns]="levelColumnDefs"
                            [rows]="levelRowData"
                            [hasFilter]="false"
                            [columnsSorting]="false"
                            [suppressColumnsMenu]="true"
                            [newRowData]="onLevelNewRow.bind(this)"
                            [showActions]="hierarchyLevels.ACTION_STATE.ADD_EDIT_DELETE"
                            (gridIsReady)="onLevelGridIsReady($event)"
                    >
                    </datagrid>
                </div>
            </p-tabPanel>
            <p-tabPanel [disabled]="editingStateService.editingState.isEditing" header="Header II">
                <ng-template pTemplate="header">
                    <i class="pi pi-table me-1"></i>
                    <span>Organization Tree</span>
                </ng-template>
                <div>
                    <datagrid
                            *ngIf="rowData"
                            id="hierarchy-tree"
                            #hierarchyTree
                            [name]="'Hierarchy Tree'"
                            [columns]="columnDefs"
                            [rows]="rowData"
                            [hasFilter]="false"
                            [suppressColumnsMenu]="true"
                            [columnsSorting]="false"
                            [treeData]="true"
                            [showActions]="hierarchyTree.ACTION_STATE.TREE_ADD_EDIT_DELETE"
                            [groupDefaultExpanded]="-1"
                            [autoGroupColumnDef]="autoGroupColumnDef"
                            [deleteRowData]="deleteNode.bind(this)"
                            (cellAction)="onCellAction($event)"
                            [getDataPath]="getDataPath"
                            (gridIsReady)="onGridIsReady($event)"
                    >
                    </datagrid>
                </div>
            </p-tabPanel>
        </p-tabView>
    </div>
</div>
