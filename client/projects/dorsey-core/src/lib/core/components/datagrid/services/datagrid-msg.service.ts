import { Injectable } from '@angular/core';
import { DataGridMessage } from '../models/datagrid-message.model';
import { Observable, Subject } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class DatagridMsgService {
  protected messageSource = new Subject<DataGridMessage>();
  message: Observable<DataGridMessage> = this.messageSource.asObservable();

  sendMessage(message: DataGridMessage): void {
    this.messageSource.next(message);
  }
}
