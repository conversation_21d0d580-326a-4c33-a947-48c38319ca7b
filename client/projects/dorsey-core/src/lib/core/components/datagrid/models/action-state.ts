import { Actions } from './actions.model';

export const ActionState = Object.freeze({
  VIEW: {
    hasCellAction: true,
    canAdd: false,
    canEdit: false,
    canView: true,
    canDelete: false,
  },
  EDIT: {
    hasCellAction: false,
    canAdd: false,
    canEdit: true,
    canView: false,
    canDelete: false,
    hasTreeAdd: false,
  },
  EDIT_DELETE: {
    hasCellAction: false,
    canAdd: false,
    canEdit: true,
    canView: false,
    canDelete: true,
    hasTreeAdd: false,
  },
  VIEW_DELETE: {
    hasCellAction: true,
    canAdd: false,
    canEdit: false,
    canView: true,
    canDelete: true,
  },
  VIEW_SINGLE_DELETE: {
    hasCellAction: true,
    canAdd: false,
    canEdit: false,
    canView: true,
    canDelete: true,
    canSingleDelete: true,
  },
  VIEW_ADD_EDIT: {
    hasCellAction: true,
    canAdd: true,
    canEdit: true,
    canDelete: false,
    canView: true,
  },
  ADD_EDIT: {
    hasCellAction: false,
    canAdd: true,
    canEdit: true,
    canDelete: false,
    canView: false,
  },
  ADD_EDIT_DELETE: {
    hasCellAction: true,
    canAdd: true,
    canEdit: true,
    canDelete: true,
    canView: false,
  },
  VIEW_ADD_EDIT_DELETE: {
    hasCellAction: true,
    canAdd: true,
    canEdit: true,
    canDelete: true,
    canView: true,
  },
  TREE_ADD_EDIT_DELETE: {
    hasCellAction: true,
    hasTreeAdd: true,
    canAdd: false,
    canEdit: true,
    canDelete: true,
    canView: false,
  },
  TREE_ADD_EDIT: {
    hasCellAction: true,
    hasTreeAdd: true,
    canAdd: false,
    canEdit: true,
    canDelete: false,
    canView: false,
  },
});
