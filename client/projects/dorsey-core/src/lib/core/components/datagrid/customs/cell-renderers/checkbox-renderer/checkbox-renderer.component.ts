import { Component } from '@angular/core';
import { ICellRendererAngularComp } from 'ag-grid-angular';
import { formatDate } from '@angular/common';

@Component({
  selector: 'dorsey-action-cell-renderer',
  templateUrl: './checkbox-renderer.component.html',
  styleUrls: ['./checkbox-renderer.component.scss'],
})
export class CheckboxRendererComponent {
  params;
  label: string;

  agInit(params): void {
    this.params = params;
  }

  onClick() {
    if (this.params.onClick instanceof Function) {
      const params = {
        select: this.params.value,
        index: this.params.rowIndex,
      };
      this.params.onClick(params);
    }
  }

  onChange() {
    this.params.data[this.params.colDef.field] = this.params.value;
  }
}
