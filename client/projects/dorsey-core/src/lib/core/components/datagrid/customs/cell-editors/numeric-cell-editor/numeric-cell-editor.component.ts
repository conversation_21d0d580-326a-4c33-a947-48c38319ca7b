import { Component } from '@angular/core';
import { ICellEditorParams } from 'ag-grid-community';
import { ICellEditorAngularComp } from 'ag-grid-angular';

@Component({
  selector: 'dorsey-numeric-cell-editor',
  templateUrl: './numeric-cell-editor.component.html',
  styleUrls: ['./numeric-cell-editor.component.scss'],
})
export class NumericCellEditorComponent implements ICellEditorAngularComp {
  params: any;
  value: string;

  agInit(params: ICellEditorParams): void {
    this.params = params;
    this.value = params.value;
  }

  refresh(params: any): boolean {
    this.value = params.value;
    return true;
  }

  onValueChange(value) {
    this.value = value;
  }

  getValue() {
    return this.value;
  }
}
