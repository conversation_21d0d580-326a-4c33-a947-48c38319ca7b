import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormBuilder, ReactiveFormsModule } from '@angular/forms';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { TitleCasePipe } from '@angular/common';

import { PaymentFormComponent } from './payment-form.component';
import { StripeService } from 'ngx-stripe';
import { PaymentService } from '../../services/payment/payment.service';
import { DynamicDialogConfig, DynamicDialogRef, DialogService } from 'primeng/dynamicdialog';
import { ToastService } from '../../services/toast.service';
import { EditingStateService } from '../../services/editing-state.service';
import { LoadingService } from '../../services/loading.service';

describe('PaymentFormComponent', () => {
  let component: PaymentFormComponent;
  let fixture: ComponentFixture<PaymentFormComponent>;

  const mockStripeService = {
    createPaymentMethod: jest.fn(),
    elements: jest.fn()
  };

  const mockPaymentService = {
    createPaymentMethod: jest.fn(),
    getCards: jest.fn()
  };

  const mockDynamicDialogConfig = {
    data: {
      amount: 100
    }
  };

  const mockDynamicDialogRef = {
    close: jest.fn()
  };

  const mockToastService = {
    displaySuccess: jest.fn(),
    displayError: jest.fn()
  };

  const mockEditingStateService = {
    editingState: {
      isEditing: false
    }
  };

  const mockLoadingService = {
    setLoading: jest.fn()
  };

  const mockDialogService = {
    open: jest.fn()
  };

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ PaymentFormComponent ],
      imports: [ ReactiveFormsModule ],
      providers: [
        FormBuilder,
        TitleCasePipe,
        { provide: StripeService, useValue: mockStripeService },
        { provide: PaymentService, useValue: mockPaymentService },
        { provide: DynamicDialogConfig, useValue: mockDynamicDialogConfig },
        { provide: DynamicDialogRef, useValue: mockDynamicDialogRef },
        { provide: ToastService, useValue: mockToastService },
        { provide: EditingStateService, useValue: mockEditingStateService },
        { provide: LoadingService, useValue: mockLoadingService },
        { provide: DialogService, useValue: mockDialogService }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    })
    .compileComponents();

    fixture = TestBed.createComponent(PaymentFormComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
