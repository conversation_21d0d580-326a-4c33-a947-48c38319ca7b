import { Component, ViewChild } from '@angular/core';
import {
  StripeCardComponent,
  StripeCardNumberComponent,
  StripeService,
} from 'ngx-stripe';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import {
  StripeCardElementOptions,
  StripeElementsOptions,
} from '@stripe/stripe-js';
import { PaymentService } from '../../services/payment/payment.service';
import {
  DialogService,
  DynamicDialogConfig,
  DynamicDialogRef,
} from 'primeng/dynamicdialog';
import { ToastService } from '../../services/toast.service';
import { SelectItem } from 'primeng/api';
import { IUserBillingInformation } from '../../models/admin/users/user-billing-information.model';
import { EditingStateService } from '../../services/editing-state.service';
import { TitleCasePipe } from '@angular/common';
import { LoadingService } from '../../services/loading.service';

@Component({
  selector: 'dorsey-payment-form',
  templateUrl: './payment-form.component.html',
  styleUrls: ['./payment-form.component.scss'],
  providers: [TitleCasePipe],
})
export class PaymentFormComponent {
  amount: number;
  cards: SelectItem[];

  selectedCard: IUserBillingInformation;

  constructor(
    private fb: FormBuilder,
    private stripeService: StripeService,
    private paymentService: PaymentService,
    public config: DynamicDialogConfig,
    private toastService: ToastService,
    private dialogService: DialogService,
    public ref: DynamicDialogRef,
    public editingStateService: EditingStateService,
    private titleCasePipe: TitleCasePipe,
    public loadingService: LoadingService
  ) {
    this.amount = config.data.amount;
  }

  ngOnInit(): void {
    this.loadCards();
  }

  onCardChange(card) {}

  pay(): void {
    if (this.selectedCard) {
      this.paymentService
        .createPaymentIntent(this.amount * 100)
        .subscribe((resp: any) => {
          setTimeout(() => this.loadingService.setLoading(true));

          const clientSecret = resp[Object.keys(resp)[0]];
          this.paymentService
            .confirmPayment(clientSecret, this.selectedCard.id)
            .subscribe((result) => {
              if (result.error) {
                this.toastService.displayError('Payment failed!');
              } else if (result.paymentIntent.status === 'succeeded') {
                this.toastService.displaySuccess('Payment succeeded!');
                this.ref.close();
              }

              this.loadingService.setLoading(false);
            });
        });
    }
  }

  private loadCards() {
    this.paymentService
      .findCards()
      .subscribe((resp: IUserBillingInformation[]) => {
        this.cards = [];

        for (const pm of resp) {
          if (pm.isPreferred) {
            setTimeout(() => {
              this.selectedCard = pm;
            });
          }

          this.cards.push({
            label: `${pm.cardNumber}: ${this.titleCasePipe.transform(
              pm.brand
            )} ${pm.isPreferred ? '(Preferred)' : ''}  ${
              pm.isExpired ? '(Expired)' : ''
            }`,
            value: pm,
          });
        }
      });
  }
}
