import {
  AfterViewInit,
  Component,
  EventEmitter,
  Input,
  OnInit,
  Output,
} from '@angular/core';
import { firstValueFrom, Subject } from 'rxjs';

import { IUser } from '../../../../models/admin/users/user.model';
import { FormBuilder, Validators } from '@angular/forms';
import { EditingStateService } from '../../../../services/editing-state.service';
import { takeUntil } from 'rxjs/operators';
import { FormAction } from '../../../../models/form-action';
import { handleCancelAction } from '../../../../utils/grid-utils';
import { UserDataService } from '../../../../services/admin/user-data.service';

@Component({
  selector: 'dorsey-profile-information',
  templateUrl: './profile-information.component.html',
  styleUrls: ['./profile-information.component.scss'],
})
export class ProfileInformationComponent implements OnInit, AfterViewInit {
  @Input() user: IUser;
  @Output() isFormValid = new EventEmitter<boolean>();
  @Output() userChange = new EventEmitter<IUser>();

  private readonly destroy$ = new Subject<void>();

  form = this.fb.group({
    firstName: ['', Validators.required],
    lastName: ['', Validators.required],
    title: [''],
    email: ['', [Validators.required, Validators.email]],
    phone: [''],
    mobile: [''],
    address: [''],
  });

  constructor(
    private fb: FormBuilder,
    public editingStateService: EditingStateService,
    private userDataService: UserDataService
  ) {
    this.editingStateService
      .getValue()
      .pipe(takeUntil(this.destroy$))
      .subscribe((value) => {
        switch (value) {
          case FormAction.SAVE:
            break;
          case FormAction.EDIT:
            this.form.enable();
            break;
          case FormAction.CANCEL:
            this.form.disable();
            break;
          case FormAction.SUBMIT:
            this.form.disable();
            break;
        }
      });
    firstValueFrom(handleCancelAction(this.editingStateService, this));
  }

  ngOnInit(): void {
    this.form.disable();
  }

  ngAfterViewInit(): void {
    this.setFormEditing();
  }

  private setFormEditing() {
    this.form.patchValue(this.user);
    this.form.valueChanges.subscribe((formValues) => {
      this.user.firstName = formValues.firstName;
      this.user.lastName = formValues.lastName;
      this.user.title = formValues.title;
      this.user.email = formValues.email;
      this.user.phone = formValues.phone;
      this.user.mobile = formValues.mobile;
      this.user.address = formValues.address;
      this.user.lastName = formValues.lastName;

      this.userDataService.setUser(this.user);
      this.isFormValid.emit(this.form.valid);
      this.userChange.emit(this.user);
    });
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
