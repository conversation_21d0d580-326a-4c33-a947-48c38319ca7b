<div *ngIf="canEdit">
  <ng-container *ngIf="editingStateService.getEditingState()">
    <p-button [appEditCta]="formAction.SAVE" class="me-3" label="Save" icon="pi pi-save" iconPos="left"></p-button>
    <p-button
      (onClick)="onCancel()"
      class="me-3 cancel-button"
      label="Cancel"
      icon="pi pi-times"
      iconPos="left"
    ></p-button>
  </ng-container>
  <ng-container *ngIf="!editingStateService.getEditingState()">
    <p-button
      [appEditCta]="formAction.EDIT"
      [disabled]="editionDisabled"
      class="me-3"
      label="Edit"
      icon="fa fa-pencil"
      iconPos="left"
    ></p-button>
  </ng-container>
</div>
