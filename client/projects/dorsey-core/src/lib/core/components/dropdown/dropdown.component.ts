import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';

@Component({
  selector: 'dorsey-dropdown',
  templateUrl: './dropdown.component.html',
  styleUrls: ['./dropdown.component.scss'],
})
export class DropdownComponent implements OnInit {
  @Input() initialValue: string;
  @Input() titleName: string;
  @Input() disabled = false;
  @Input() showPrompt = false;
  @Input() prompt = 'Select';
  @Input() options: any[];
  @Input() fieldName: string;
  @Input() fieldLabel: string;
  @Output() selectionChanged = new EventEmitter<any>();

  selectedItem: string = null;

  constructor() {}

  ngOnInit(): void {
    if (this.initialValue) {
      this.selectedItem = this.initialValue;
    }
  }

  onSelectionChanged(selectedItem): void {
    this.selectedItem = selectedItem;
    this.selectionChanged.emit(selectedItem);
  }
}
