export interface IUserBillingInformation {
  id: string;
  cardholderName?: string;
  cardNumber?: string;
  brand?: string;
  expDate?: string;
  isExpired?: boolean;
  isPreferred?: boolean;
  country?: string;
  state?: string;
  city?: string;
  addressLine1?: string;
  addressLine2?: string;
  zipCode?: string;
}

export class UserBillingInformation implements IUserBillingInformation {
  constructor(
    public id: string,
    public cardholderName?: string,
    public cardNumber?: string,
    public brand?: string,
    public expDate?: string,
    public isExpired?: boolean,
    public isPreferred?: boolean,
    public country?: string,
    public state?: string,
    public city?: string,
    public addressLine1?: string,
    public addressLine2?: string,
    public zipCode?: string
  ) {}
}
