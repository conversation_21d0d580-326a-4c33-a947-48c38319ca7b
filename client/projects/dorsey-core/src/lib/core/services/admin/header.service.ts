import { Injectable } from '@angular/core';
import { DomSanitizer, SafeUrl } from '@angular/platform-browser';
import { MenuItem } from 'primeng/api';

@Injectable({
  providedIn: 'root',
})
export class HeaderService {
  title: string;
  selectedMenuOption: string;
  logoUrl: SafeUrl;
  skin: number;
  breadCrumbItems: MenuItem[];

  constructor(private domSanitizationService: DomSanitizer) {}

  setLogo(file: Blob | MediaSource) {
    this.logoUrl = this.domSanitizationService.bypassSecurityTrustUrl(
      URL.createObjectURL(file)
    );
  }
}
