import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { BaseRestService } from '../base-rest.service';
import { ICaseStatusTransition } from '../../models/state-machine/case-status-transition';

@Injectable()
export class StateMachineService extends BaseRestService {
  findAllCaseStatusTransitions(): Observable<any> {
    return this.get(`stateMachine/caseStatusTransition`);
  }
  updateCaseStatusTransitions(
    transitions: ICaseStatusTransition[]
  ): Observable<any> {
    return this.put(`stateMachine/caseStatusTransition`, transitions);
  }
}
