import { Injectable } from '@angular/core';

@Injectable({ providedIn: 'root' })
export class LoadingService {

  public isLoading = false;
  constructor() { }

  setLoading(val: boolean): void {
    this.isLoading = val;
  }

  public show(): void {
    // Hack avoiding `ExpressionChangedAfterItHasBeenCheckedError` error
    Promise.resolve(null).then(() => {
      this.isLoading = true;
    });
  }

  public hide(): void {
    this.isLoading = false;
  }
}
