import { Inject, Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { DorseyConfiguration } from '../models/configuration';

@Injectable({
  providedIn: 'root',
})
export class BaseRestService {
  protected baseURL: string;
  protected headers: HttpHeaders;

  constructor(
    @Inject('config') protected config: DorseyConfiguration,
    protected httpClient: HttpClient
  ) {
    this.baseURL = config.environment.apiUrl;

    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json; charset=utf-8');
    headers = headers.set('Referrer-Policy', 'no-referrer');
    this.headers = headers;
  }

  protected get(resource: string, params?: HttpParams): Observable<any> {
    return this.httpClient.get(this.baseURL + '/' + resource, {
      headers: this.headers,
      params,
    });
  }

  protected head(resource: string, params?: HttpParams): Observable<object> {
    return this.httpClient.head(this.baseURL + '/' + resource, {
      headers: this.headers,
      params,
    });
  }

  protected post(
    resource: string,
    data?: any,
    params?: HttpParams
  ): Observable<any> {
    return this.httpClient.post(this.baseURL + '/' + resource, data, {
      headers: this.headers,
      params,
    });
  }

  protected put(
    resource: string,
    data?: any,
    params?: HttpParams
  ): Observable<any> {
    return this.httpClient.put(this.baseURL + '/' + resource, data, {
      headers: this.headers,
      params,
    });
  }

  protected delete(resource: string, params?: HttpParams): Observable<object> {
    return this.httpClient.delete(this.baseURL + '/' + resource, {
      headers: this.headers,
      params,
    });
  }
}
