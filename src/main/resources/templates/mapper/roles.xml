<?xml version="1.0" encoding="ISO-8859-1"?>
<workbook>
  <worksheet idx="0" name="page">
    <section startRow="0" endRow="0">
      <mapping cell="A1">header.roleName</mapping>
      <mapping cell="B1">header.department</mapping>
      <mapping cell="C1">header.description</mapping>
    </section>
    <loop startRow="0" endRow="0" items="rows" var="row"
          varType="com.dorsey.core.service.dataload.processors.rows.RoleRow">
      <section startRow="0" endRow="0">
        <mapping row="0" col="0" nullAllowed="true">row.roleName</mapping>
        <mapping row="0" col="1" nullAllowed="true">row.department</mapping>
        <mapping row="0" col="2" nullAllowed="true">row.description</mapping>
      </section>
      <loopbreakcondition>
        <rowcheck offset="0">
        </rowcheck>
      </loopbreakcondition>
    </loop>
  </worksheet>
</workbook>
