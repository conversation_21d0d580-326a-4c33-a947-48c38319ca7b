<?xml version="1.0" encoding="ISO-8859-1"?>
<workbook>
  <worksheet idx="0" name="page">
    <section startRow="0" endRow="0">
      <mapping cell="A1">header.firstname</mapping>
      <mapping cell="B1">header.lastname</mapping>
      <mapping cell="C1">header.title</mapping>
      <mapping cell="D1">header.email</mapping>
      <mapping cell="E1">header.hierarchyName</mapping>
      <mapping cell="F1">header.roles</mapping>
      <mapping cell="G1">header.effectiveDate</mapping>
      <mapping cell="H1">header.terminationDate</mapping>
      <mapping cell="I1">header.status</mapping>
    </section>
    <loop startRow="0" endRow="0" items="rows" var="row"
          varType="com.dorsey.core.service.dataload.processors.rows.UserRow">
      <section startRow="0" endRow="0">
        <mapping row="0" col="0" nullAllowed="true">row.firstName</mapping>
        <mapping row="0" col="1" nullAllowed="true">row.lastName</mapping>
        <mapping row="0" col="2" nullAllowed="true">row.title</mapping>
        <mapping row="0" col="3" nullAllowed="true">row.email</mapping>
        <mapping row="0" col="4" nullAllowed="true">row.hierarchyName</mapping>
        <mapping row="0" col="5" nullAllowed="true">row.roles</mapping>
        <mapping row="0" col="6" nullAllowed="true">row.effectiveDate</mapping>
        <mapping row="0" col="7" nullAllowed="true">row.terminationDate</mapping>
        <mapping row="0" col="8" nullAllowed="true">row.status</mapping>
      </section>
      <loopbreakcondition>
        <rowcheck offset="0">
        </rowcheck>
      </loopbreakcondition>
    </loop>
  </worksheet>
</workbook>
