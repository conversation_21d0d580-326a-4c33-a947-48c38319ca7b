<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: white;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
        }
        .login-container {
            width: 400px;
            padding: 40px;
            background-color: white;
            box-shadow: 0px 4px 16px rgba(0, 0, 0, 0.1);
            border-radius: 8px;
        }
        .login-container h2 {
            text-align: center;
            margin-bottom: 20px;
        }
        .login-container input[type="email"],
        .login-container input[type="password"] {
            width: auto;
            padding: 12px;
            margin: 10px 0;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-size: 14px;
        }
        .login-container button[type="submit"] {
            width: 100%;
            padding: 12px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 16px;
            cursor: pointer;
            margin-top: 10px;
        }
        .login-container button[type="submit"]:hover {
            background-color: #0056b3;
        }
        .oauth-buttons-left, .oauth-buttons-center, .oauth-buttons-right {
            display: flex;
            flex-direction: column;
        }
        .oauth-buttons-left, .oauth-buttons-center {
            margin-right: 30px;
        }
        .oauth-buttons {
            margin-top: 20px;
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: center;
        }
        .oauth-button {
            width: 100%;
            padding: 12px;
            margin: 8px 0;
            font-size: 16px;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-align: center;
        }
        .oauth-button.google {
            background-color: #db4437;
        }
        .oauth-button.google:hover {
            background-color: #c33d2e;
        }
        .oauth-button.facebook {
            background-color: #3b5998;
        }
        .oauth-button.facebook:hover {
            background-color: #334d88;
        }
        .oauth-button.apple {
            background-color: #000000;
        }
        .oauth-button.apple:hover {
            background-color: #333333;
        }
        .divider {
            display: flex;
            align-items: center;
            margin: 25px 0;
        }
        .divider::before,
        .divider::after {
            content: '';
            flex: 1;
            height: 1px;
            background-color: #ccc;
        }
        .divider::before {
            margin-right: 10px;
        }
        .divider::after {
            margin-left: 10px;
        }
        /*.forgot-password {*/
        /*    display: block;*/
        /*    text-align: center;*/
        /*    margin-top: 10px;*/
        /*    color: #007bff;*/
        /*    text-decoration: none;*/
        /*}*/

        .account-management {
            display: flex;
            justify-content: space-between;
            padding: 12px;
        }
        .account-management a {
            color: #007bff;
            text-decoration: none;
        }
        .forgot-password a:hover {
            color: #0056b3;
        }
        .create-account, .forgot-password {
            display: flex;
        }
        .create-account a:hover, .forgot-password a:hover {
            color: #0056b3;
        }
        .oauth-buttons a {
            display: flex;
            align-items: center;
            text-decoration: none;
            padding: 2px;
        }

        .oauth-buttons a:hover {
            color: #0056b3;
            font-weight: 600;
        }

        .oauth-buttons svg {
            width: 28px;
            margin: 0 5px 0 5px;
        }
    </style>
</head>
<body>

<div class="login-container">
    <h2>Sign In</h2>

    <div class="oauth-buttons">
        <a href="/api/saml2/authenticate/okta-saml" style="width: 100%">
            <button class="oauth-button google">Enterprise Login</button>
        </a>
    </div>

    <div class="divider">Or</div>

    <form th:action="@{/login}" method="post">
        <input type="email" th:name="email" placeholder="Email" required>
        <input type="password" th:name="password" placeholder="Password" required>
        <button type="submit">Login</button>
    </form>

    <div class="account-management">
        <div class="forgot-password">
            <a href="#">Forgot password?</a>
        </div>
        <div class="create-account">
            <span>Don't have an account? <a href="#">Create</a></span>
        </div>
    </div>

    <div class="divider">Or continue with</div>

    <div class="oauth-buttons">
        <div class="oauth-buttons-left">
            <a href="">
                <svg id="Layer_1" data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 122.88 122.31"><defs><style>.cls-1{fill:#0a66c2;}.cls-1,.cls-2{fill-rule:evenodd;}.cls-2{fill:#fff;}</style></defs><title>linkedin-app</title><path class="cls-1" d="M27.75,0H95.13a27.83,27.83,0,0,1,27.75,27.75V94.57a27.83,27.83,0,0,1-27.75,27.74H27.75A27.83,27.83,0,0,1,0,94.57V27.75A27.83,27.83,0,0,1,27.75,0Z"/><path class="cls-2" d="M49.19,47.41H64.72v8h.22c2.17-3.88,7.45-8,15.34-8,16.39,0,19.42,10.2,19.42,23.47V98.94H83.51V74c0-5.71-.12-13.06-8.42-13.06s-9.72,6.21-9.72,12.65v25.4H49.19V47.41ZM40,31.79a8.42,8.42,0,1,1-8.42-8.42A8.43,8.43,0,0,1,40,31.79ZM23.18,47.41H40V98.94H23.18V47.41Z"/></svg>
                <span>Linkedin</span>
            </a>
            <a href="/api/oauth2/authorization/google">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 326667 333333" shape-rendering="geometricPrecision" text-rendering="geometricPrecision" image-rendering="optimizeQuality" fill-rule="evenodd" clip-rule="evenodd"><path d="M326667 170370c0-13704-1112-23704-3518-34074H166667v61851h91851c-1851 15371-11851 38519-34074 54074l-311 2071 49476 38329 3428 342c31481-29074 49630-71852 49630-122593m0 0z" fill="#4285f4"/><path d="M166667 333333c44999 0 82776-14815 110370-40370l-52593-40742c-14074 9815-32963 16667-57777 16667-44074 0-81481-29073-94816-69258l-1954 166-51447 39815-673 1870c27407 54444 83704 91852 148890 91852z" fill="#34a853"/><path d="M71851 199630c-3518-10370-5555-21482-5555-32963 0-11482 2036-22593 5370-32963l-93-2209-52091-40455-1704 811C6482 114444 1 139814 1 166666s6482 52221 17777 74814l54074-41851m0 0z" fill="#fbbc04"/><path d="M166667 64444c31296 0 52406 13519 64444 24816l47037-45926C249260 16482 211666 1 166667 1 101481 1 45185 37408 17777 91852l53889 41853c13520-40185 50927-69260 95001-69260m0 0z" fill="#ea4335"/></svg>
                <span>Google</span>
            </a>
        </div>
        <div class="oauth-buttons-center">
            <a href="/api/oauth2/authorization/microsoft">
                <svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" viewBox="0 0 512 512" style="enable-background:new 0 0 512 512" xml:space="preserve"><style type="text/css">.st0{fill:#0A2767;} .st1{fill:#0364B8;} .st2{fill:#0078D4;} .st3{fill:#28A8EA;} .st4{fill:#14447D;} .st5{fill:url(#SVGID_1_);} .st6{opacity:0.5;fill:#0A2767;enable-background:new ;} .st7{fill:#1490DF;} .st8{opacity:0.1;enable-background:new ;} .st9{opacity:0.05;enable-background:new ;} .st10{opacity:0.2;enable-background:new ;} .st11{fill:url(#SVGID_2_);} .st12{fill:#FFFFFF;} .st13{fill:#50D9FF;}</style><path class="st0" d="M512,267.91c0.03-4-2.04-7.73-5.45-9.82h-0.06l-0.21-0.12L328.86,152.95c-0.77-0.52-1.56-0.99-2.38-1.42	c-6.85-3.53-14.99-3.53-21.84,0c-0.82,0.43-1.62,0.9-2.38,1.42L124.84,257.96l-0.21,0.12c-5.42,3.37-7.08,10.5-3.71,15.92	c0.99,1.6,2.36,2.93,3.99,3.88L302.32,382.9c0.77,0.51,1.56,0.99,2.38,1.42c6.85,3.53,14.99,3.53,21.84,0	c0.82-0.43,1.61-0.9,2.38-1.42l177.41-105.02C509.88,275.82,512.04,272.01,512,267.91z"/><path class="st1" d="M145.53,197.79h116.43v106.72H145.53V197.79z M488.19,89.3V40.48c0.28-12.21-9.38-22.33-21.59-22.62H164.47	c-12.21,0.29-21.87,10.42-21.59,22.62V89.3l178.6,47.63L488.19,89.3z"/><path class="st2" d="M142.88,89.3h119.07v107.16H142.88V89.3z"/><path class="st3" d="M381.02,89.3H261.95v107.16l119.07,107.16h107.16V196.47L381.02,89.3z"/><path class="st2" d="M261.95,196.47h119.07v107.16H261.95V196.47z"/><path class="st1" d="M261.95,303.63h119.07v107.16H261.95V303.63z"/><path class="st4" d="M145.53,304.51h116.43v97.02H145.53V304.51z"/><path class="st2" d="M381.02,303.63h107.16v107.16H381.02V303.63z"/><linearGradient id="SVGID_1_" gradientUnits="userSpaceOnUse" x1="315.5344" y1="-877.4263" x2="315.5344" y2="-651.1933" gradientTransform="matrix(1 0 0 1 0 1145.3334)"><stop offset="0" style="stop-color:#35B8F1"/><stop offset="1" style="stop-color:#28A8EA"/></linearGradient><path class="st5" d="M506.55,277.23l-0.23,0.12l-177.41,99.78c-0.77,0.48-1.56,0.93-2.38,1.33c-3.01,1.43-6.29,2.25-9.62,2.38	l-9.69-5.67c-0.82-0.41-1.61-0.87-2.38-1.37l-179.8-102.61h-0.08l-5.88-3.29V469.9c0.09,13.48,11.09,24.33,24.56,24.24h344.18	c0.2,0,0.38-0.1,0.6-0.1c2.85-0.18,5.65-0.77,8.33-1.74c1.16-0.49,2.28-1.07,3.35-1.74c0.8-0.45,2.17-1.44,2.17-1.44	c6.1-4.51,9.71-11.64,9.74-19.23V267.91C512,271.77,509.91,275.33,506.55,277.23z"/><path class="st6" d="M502.47,267.11v12.38L316.96,407.22L124.9,271.28c0-0.07-0.05-0.12-0.12-0.12l0,0l-17.62-10.6v-8.93l7.26-0.12	l15.36,8.81l0.36,0.12l1.31,0.83c0,0,180.51,103,180.99,103.23l6.91,4.05c0.6-0.24,1.19-0.48,1.91-0.71	c0.36-0.24,179.2-100.85,179.2-100.85L502.47,267.11z"/><path class="st7" d="M506.55,277.23l-0.23,0.13l-177.41,99.78c-0.77,0.48-1.56,0.93-2.38,1.33c-6.89,3.37-14.95,3.37-21.84,0	c-0.82-0.4-1.61-0.85-2.38-1.33l-177.41-99.78l-0.21-0.13c-3.43-1.86-5.57-5.43-5.61-9.32V469.9c0.09,13.47,11.08,24.33,24.55,24.24	c0,0,0,0,0,0h343.83c13.47,0.09,24.47-10.77,24.55-24.24c0,0,0,0,0,0V267.91C512,271.77,509.91,275.33,506.55,277.23z"/><path class="st8" d="M331.49,375.67l-2.66,1.49c-0.77,0.49-1.56,0.94-2.38,1.35c-2.93,1.44-6.11,2.28-9.36,2.48l67.5,79.82	l117.75,28.37c3.23-2.44,5.79-5.64,7.47-9.32L331.49,375.67z"/><path class="st9" d="M343.52,368.9l-14.68,8.25c-0.77,0.49-1.56,0.94-2.38,1.35c-2.93,1.44-6.11,2.28-9.36,2.48l31.62,87.19	l153.66,20.97c6.05-4.54,9.62-11.67,9.62-19.24v-2.61L343.52,368.9z"/><path class="st3" d="M143.96,494.14h343.46c5.29,0.03,10.44-1.64,14.7-4.76L307.2,375.2c-0.82-0.41-1.61-0.87-2.38-1.37	l-179.8-102.61h-0.08l-5.87-3.31v201.3C119.06,482.96,130.2,494.13,143.96,494.14C143.96,494.14,143.96,494.14,143.96,494.14z"/><path class="st8" d="M285.77,134.94v253.98c-0.02,8.9-5.44,16.91-13.69,20.24c-2.56,1.1-5.31,1.67-8.1,1.67H119.07v-285.8h23.81	v-11.91h121.09C276.01,113.16,285.74,122.91,285.77,134.94z"/><path class="st10" d="M273.86,146.85v253.98c0.03,2.88-0.58,5.72-1.79,8.33c-3.31,8.15-11.21,13.5-20,13.54h-133V125.02h133	c3.45-0.03,6.86,0.83,9.88,2.5C269.25,131.2,273.86,138.68,273.86,146.85z"/><path class="st10" d="M273.86,146.85v230.16c-0.06,12.02-9.77,21.77-21.79,21.87h-133V125.02h133c3.45-0.03,6.86,0.83,9.88,2.5	C269.25,131.2,273.86,138.68,273.86,146.85z"/><path class="st10" d="M261.95,146.85v230.16c-0.01,12.04-9.75,21.81-21.79,21.87H119.07V125.02h121.09	c12.04,0.01,21.8,9.77,21.79,21.81C261.95,146.84,261.95,146.84,261.95,146.85z"/><linearGradient id="SVGID_2_" gradientUnits="userSpaceOnUse" x1="45.5066" y1="-1037.3639" x2="216.4467" y2="-741.3027" gradientTransform="matrix(1 0 0 1 0 1145.3334)"><stop offset="0" style="stop-color:#1784D9"/><stop offset="0.5" style="stop-color:#107AD5"/><stop offset="1" style="stop-color:#0A63C9"/></linearGradient><path class="st11" d="M21.83,125.02h218.3c12.05,0,21.83,9.77,21.83,21.83v218.3c0,12.05-9.77,21.83-21.83,21.83H21.83	C9.77,386.98,0,377.2,0,365.15v-218.3C0,134.8,9.77,125.02,21.83,125.02z"/><path class="st12" d="M68.22,216.56c5.38-11.46,14.06-21.05,24.93-27.54c12.04-6.89,25.75-10.33,39.61-9.93	c12.85-0.28,25.53,2.98,36.66,9.42c10.46,6.24,18.89,15.38,24.25,26.31c5.85,12.05,8.76,25.31,8.5,38.7	c0.28,13.99-2.71,27.86-8.75,40.48c-5.49,11.33-14.19,20.79-25,27.23c-11.56,6.64-24.71,9.98-38.03,9.67	c-13.13,0.32-26.09-2.98-37.47-9.53c-10.55-6.25-19.08-15.4-24.58-26.36c-5.88-11.87-8.83-24.99-8.6-38.23	C59.5,242.91,62.4,229.16,68.22,216.56z M94.79,281.22c2.87,7.25,7.73,13.53,14.03,18.12c6.41,4.48,14.09,6.79,21.91,6.6	c8.33,0.33,16.54-2.06,23.39-6.81c6.22-4.58,10.95-10.88,13.62-18.12c2.99-8.09,4.46-16.66,4.35-25.28c0.09-8.7-1.29-17.36-4.1-25.6	c-2.48-7.44-7.06-14-13.19-18.88c-6.68-4.97-14.86-7.5-23.18-7.14c-7.99-0.21-15.84,2.12-22.42,6.66	c-6.4,4.61-11.36,10.95-14.29,18.28c-6.5,16.79-6.54,35.4-0.1,52.21L94.79,281.22z"/><path class="st13" d="M381.02,89.3h107.16v107.16H381.02V89.3z"/></svg>
                <span>Outlook</span>
            </a>
            <a href="/api/oauth2/authorization/facebook">
                <svg xmlns="http://www.w3.org/2000/svg" shape-rendering="geometricPrecision" text-rendering="geometricPrecision" image-rendering="optimizeQuality" fill-rule="evenodd" clip-rule="evenodd" viewBox="0 0 509 509"><g fill-rule="nonzero"><path fill="#0866FF" d="M509 254.5C509 113.94 395.06 0 254.5 0S0 113.94 0 254.5C0 373.86 82.17 474 193.02 501.51V332.27h-52.48V254.5h52.48v-33.51c0-86.63 39.2-126.78 124.24-126.78 16.13 0 43.95 3.17 55.33 6.33v70.5c-6.01-.63-16.44-.95-29.4-.95-41.73 0-57.86 15.81-57.86 56.91v27.5h83.13l-14.28 77.77h-68.85v174.87C411.35 491.92 509 384.62 509 254.5z"/><path fill="#fff" d="M354.18 332.27l14.28-77.77h-83.13V227c0-41.1 16.13-56.91 57.86-56.91 12.96 0 23.39.32 29.4.95v-70.5c-11.38-3.16-39.2-6.33-55.33-6.33-85.04 0-124.24 40.16-124.24 126.78v33.51h-52.48v77.77h52.48v169.24c19.69 4.88 40.28 7.49 61.48 7.49 10.44 0 20.72-.64 30.83-1.86V332.27h68.85z"/></g></svg>
                <span>Facebook</span>
            </a>
        </div>
        <div class="oauth-buttons-right">
            <a href="">
                <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 132.004 132"><defs><linearGradient id="b"><stop offset="0" stop-color="#3771c8"/><stop stop-color="#3771c8" offset=".128"/><stop offset="1" stop-color="#60f" stop-opacity="0"/></linearGradient><linearGradient id="a"><stop offset="0" stop-color="#fd5"/><stop offset=".1" stop-color="#fd5"/><stop offset=".5" stop-color="#ff543e"/><stop offset="1" stop-color="#c837ab"/></linearGradient><radialGradient id="c" cx="158.429" cy="578.088" r="65" xlink:href="#a" gradientUnits="userSpaceOnUse" gradientTransform="matrix(0 -1.98198 1.8439 0 -1031.402 454.004)" fx="158.429" fy="578.088"/><radialGradient id="d" cx="147.694" cy="473.455" r="65" xlink:href="#b" gradientUnits="userSpaceOnUse" gradientTransform="matrix(.17394 .86872 -3.5818 .71718 1648.348 -458.493)" fx="147.694" fy="473.455"/></defs><path fill="url(#c)" d="M65.03 0C37.888 0 29.95.028 28.407.156c-5.57.463-9.036 1.34-12.812 3.22-2.91 1.445-5.205 3.12-7.47 5.468C4 13.126 1.5 18.394.595 24.656c-.44 3.04-.568 3.66-.594 19.188-.01 5.176 0 11.988 0 21.125 0 27.12.03 35.05.16 36.59.45 5.42 1.3 8.83 3.1 12.56 3.44 7.14 10.01 12.5 17.75 14.5 2.68.69 5.64 1.07 9.44 1.25 1.61.07 18.02.12 34.44.12 16.42 0 32.84-.02 34.41-.1 4.4-.207 6.955-.55 9.78-1.28 7.79-2.01 14.24-7.29 17.75-14.53 1.765-3.64 2.66-7.18 3.065-12.317.088-1.12.125-18.977.125-36.81 0-17.836-.04-35.66-.128-36.78-.41-5.22-1.305-8.73-3.127-12.44-1.495-3.037-3.155-5.305-5.565-7.624C116.9 4 111.64 1.5 105.372.596 102.335.157 101.73.027 86.19 0H65.03z" transform="translate(1.004 1)"/><path fill="url(#d)" d="M65.03 0C37.888 0 29.95.028 28.407.156c-5.57.463-9.036 1.34-12.812 3.22-2.91 1.445-5.205 3.12-7.47 5.468C4 13.126 1.5 18.394.595 24.656c-.44 3.04-.568 3.66-.594 19.188-.01 5.176 0 11.988 0 21.125 0 27.12.03 35.05.16 36.59.45 5.42 1.3 8.83 3.1 12.56 3.44 7.14 10.01 12.5 17.75 14.5 2.68.69 5.64 1.07 9.44 1.25 1.61.07 18.02.12 34.44.12 16.42 0 32.84-.02 34.41-.1 4.4-.207 6.955-.55 9.78-1.28 7.79-2.01 14.24-7.29 17.75-14.53 1.765-3.64 2.66-7.18 3.065-12.317.088-1.12.125-18.977.125-36.81 0-17.836-.04-35.66-.128-36.78-.41-5.22-1.305-8.73-3.127-12.44-1.495-3.037-3.155-5.305-5.565-7.624C116.9 4 111.64 1.5 105.372.596 102.335.157 101.73.027 86.19 0H65.03z" transform="translate(1.004 1)"/><path fill="#fff" d="M66.004 18c-13.036 0-14.672.057-19.792.29-5.11.234-8.598 1.043-11.65 2.23-3.157 1.226-5.835 2.866-8.503 5.535-2.67 2.668-4.31 5.346-5.54 8.502-1.19 3.053-2 6.542-2.23 11.65C18.06 51.327 18 52.964 18 66s.058 14.667.29 19.787c.235 5.11 1.044 8.598 2.23 11.65 1.227 3.157 2.867 5.835 5.536 8.503 2.667 2.67 5.345 4.314 8.5 5.54 3.054 1.187 6.543 1.996 11.652 2.23 5.12.233 6.755.29 19.79.29 13.037 0 14.668-.057 19.788-.29 5.11-.234 8.602-1.043 11.656-2.23 3.156-1.226 5.83-2.87 8.497-5.54 2.67-2.668 4.31-5.346 5.54-8.502 1.18-3.053 1.99-6.542 2.23-11.65.23-5.12.29-6.752.29-19.788 0-13.036-.06-14.672-.29-19.792-.24-5.11-1.05-8.598-2.23-11.65-1.23-3.157-2.87-5.835-5.54-8.503-2.67-2.67-5.34-4.31-8.5-5.535-3.06-1.187-6.55-1.996-11.66-2.23-5.12-.233-6.75-.29-19.79-.29zm-4.306 8.65c1.278-.002 2.704 0 4.306 0 12.816 0 14.335.046 19.396.276 4.68.214 7.22.996 8.912 1.653 2.24.87 3.837 1.91 5.516 3.59 1.68 1.68 2.72 3.28 3.592 5.52.657 1.69 1.44 4.23 1.653 8.91.23 5.06.28 6.58.28 19.39s-.05 14.33-.28 19.39c-.214 4.68-.996 7.22-1.653 8.91-.87 2.24-1.912 3.835-3.592 5.514-1.68 1.68-3.275 2.72-5.516 3.59-1.69.66-4.232 1.44-8.912 1.654-5.06.23-6.58.28-19.396.28-12.817 0-14.336-.05-19.396-.28-4.68-.216-7.22-.998-8.913-1.655-2.24-.87-3.84-1.91-5.52-3.59-1.68-1.68-2.72-3.276-3.592-5.517-.657-1.69-1.44-4.23-1.653-8.91-.23-5.06-.276-6.58-.276-19.398s.046-14.33.276-19.39c.214-4.68.996-7.22 1.653-8.912.87-2.24 1.912-3.84 3.592-5.52 1.68-1.68 3.28-2.72 5.52-3.592 1.692-.66 4.233-1.44 8.913-1.655 4.428-.2 6.144-.26 15.09-.27zm29.928 7.97c-3.18 0-5.76 2.577-5.76 5.758 0 3.18 2.58 5.76 5.76 5.76 3.18 0 5.76-2.58 5.76-5.76 0-3.18-2.58-5.76-5.76-5.76zm-25.622 6.73c-13.613 0-24.65 11.037-24.65 24.65 0 13.613 11.037 24.645 24.65 24.645C79.617 90.645 90.65 79.613 90.65 66S79.616 41.35 66.003 41.35zm0 8.65c8.836 0 16 7.163 16 16 0 8.836-7.164 16-16 16-8.837 0-16-7.164-16-16 0-8.837 7.163-16 16-16z"/></svg>
                <span>Instagram</span>
            </a>
            <a href="">
                <svg xmlns="http://www.w3.org/2000/svg" shape-rendering="geometricPrecision" text-rendering="geometricPrecision" image-rendering="optimizeQuality" fill-rule="evenodd" clip-rule="evenodd" viewBox="0 0 512 509.64"><rect width="512" height="509.64" rx="115.61" ry="115.61"/><path fill="#fff" fill-rule="nonzero" d="M323.74 148.35h36.12l-78.91 90.2 92.83 122.73h-72.69l-56.93-74.43-65.15 74.43h-36.14l84.4-96.47-89.05-116.46h74.53l51.46 68.04 59.53-68.04zm-12.68 191.31h20.02l-129.2-170.82H180.4l130.66 170.82z"/></svg>
                <span>Twitter</span>
            </a>
        </div>
    </div>
</div>

</body>
</html>
