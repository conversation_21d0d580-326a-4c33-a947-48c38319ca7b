package com.dorsey.core.filter;

import com.dorsey.core.service.logging.ServiceCallLogger;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.lang.NonNull;
import org.springframework.web.filter.OncePerRequestFilter;
import org.springframework.web.util.ContentCachingRequestWrapper;
import org.springframework.web.util.ContentCachingResponseWrapper;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;

@Configuration
public class ServiceCallLoggingFilter extends OncePerRequestFilter {
  private static final Logger LOGGER = LoggerFactory.getLogger(ServiceCallLoggingFilter.class);
  private final ServiceCallLogger serviceCallLogger;

  public ServiceCallLoggingFilter(ServiceCallLogger serviceCallLogger) {
    this.serviceCallLogger = serviceCallLogger;
  }

  @Bean
  public FilterRegistrationBean<ServiceCallLoggingFilter> registrationForLoggingFilter(ServiceCallLoggingFilter filter) {
    FilterRegistrationBean<ServiceCallLoggingFilter> registration = new FilterRegistrationBean<>(filter);
    registration.setEnabled(true);
    return registration;
  }

  @Override
  protected void doFilterInternal(@NonNull HttpServletRequest request, @NonNull HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
    serviceCallLogger.init();
    ContentCachingRequestWrapper requestToUse = new ContentCachingRequestWrapper(request, 10000);
    ContentCachingResponseWrapper responseToUse = new ContentCachingResponseWrapper(response);

    try {
      filterChain.doFilter(requestToUse, responseToUse);
    } finally {
      try {
        serviceCallLogger.finish(requestToUse, responseToUse);
        responseToUse.copyBodyToResponse();
      } catch (Exception e) {
        LOGGER.error("", e);
      }
    }
  }
}
