package com.dorsey.core.model.logging;

import com.dorsey.core.enums.AuditAction;
import com.dorsey.core.model.AbstractModel;
import com.dorsey.core.model.user.Users;

import com.vladmihalcea.hibernate.type.json.JsonType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.annotations.Type;
import org.hibernate.type.SqlTypes;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import java.time.LocalDateTime;
import java.util.UUID;

@Builder
@AllArgsConstructor
@Entity
@Getter
@Setter
@NoArgsConstructor
public class AuditLog {
  @Id
  private UUID auditId;
  private UUID userId;
  private String actionCode;
  private String entityCode;
  private String entityKey;
  private LocalDateTime auditDate;
  @JdbcTypeCode(SqlTypes.JSON)
  @Type(JsonType.class)
  @Column(columnDefinition = "json")
  private Object entityValue;

  public AuditLog(String id, UUID userId, String action, String entityCode, String entityKey, LocalDateTime auditDate) {
    this.auditId = UUID.fromString(id);
    this.userId = userId;
    this.actionCode = action;
    this.entityCode = entityCode;
    this.entityKey = entityKey;
    this.auditDate = auditDate;
  }
  public AuditLog(String id, UUID userId, String action, String entityCode, String entityKey, LocalDateTime auditDate, Object entityValue) {
    this.auditId = UUID.fromString(id);
    this.userId = userId;
    this.actionCode = action;
    this.entityCode = entityCode;
    this.entityKey = entityKey;
    this.auditDate = auditDate;
    this.entityValue = entityValue;
  }

  public AuditLog(AuditAction action, Object entity, Users user) {
    this.auditId = UUID.randomUUID();
    this.actionCode = action.getLabel();
    this.auditDate = LocalDateTime.now();
    this.userId = null == user ? UUID.fromString("00000000-0000-0000-0000-000000000000") : user.getUserId();
    this.entityCode = entity.getClass().getSimpleName();
    this.entityKey = ((AbstractModel) entity).key();
    this.entityValue = entity;
  }
}
