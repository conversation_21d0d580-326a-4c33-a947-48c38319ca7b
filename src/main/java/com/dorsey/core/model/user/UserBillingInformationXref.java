package com.dorsey.core.model.user;

import com.dorsey.core.dto.users.UserBillingInformationDTO;
import com.dorsey.core.dto.users.UserContactDTO;
import com.dorsey.core.model.AbstractModel;
import com.dorsey.core.service.AuditListener;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EntityListeners;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.MapsId;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Set;
import java.util.UUID;

@Builder(toBuilder=true)
@AllArgsConstructor
@EntityListeners(AuditListener.class)
@Entity
@NoArgsConstructor
@Getter
@Setter
public class UserBillingInformationXref extends AbstractModel {
  @Id
  private UUID userId;
  private String stripeCustomerId;


  @OneToOne
  @JoinColumn(name = "userId", referencedColumnName = "userId", insertable = false, updatable = false)
  private Users user;

  @OneToMany(fetch = FetchType.EAGER)
  @JoinColumn(name = "stripeCustomerId", referencedColumnName = "stripeCustomerId", insertable = false, updatable = false)
  private Set<UserCreditCard> userCreditCards;

  @Override
  public String key() {
    return userId.toString();
  }
}
