package com.dorsey.core.model.user;

import com.dorsey.core.converter.UUIDConverter;
import com.dorsey.core.dto.roles.RoleDTO;
import com.dorsey.core.dto.users.User;
import com.dorsey.core.dto.users.UserBillingInformationDTO;
import com.dorsey.core.dto.users.UserContactDTO;
import com.dorsey.core.model.AbstractModel;
import com.dorsey.core.model.organization.HierarchyNode;
import com.dorsey.core.service.AuditListener;
import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.annotations.Type;
import org.springframework.util.Assert;

import jakarta.persistence.CascadeType;
import jakarta.persistence.Entity;
import jakarta.persistence.EntityListeners;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OneToOne;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotNull;

import java.sql.Types;
import java.time.LocalDateTime;
import java.util.Set;
import java.util.UUID;

@Builder(toBuilder=true)
@AllArgsConstructor
@EntityListeners(AuditListener.class)
@Entity
@Table(name="users")
@NoArgsConstructor
@Getter
@Setter
public class Users extends AbstractModel {
  @Id
  @Convert(converter = UUIDConverter.class)
  private UUID userId;
  @Email
  private String email;
  @NotNull
  private Boolean isActive;
  @NotNull
  private String firstName;
  @NotNull
  private String lastName;
  private String title;
  private Integer hierarchyId;

  @OneToOne
  @JoinColumn(name = "userId", referencedColumnName = "userId")
  private UserContactXref contacts;

//  @OneToOne(cascade= CascadeType.ALL, fetch = FetchType.EAGER, orphanRemoval = true, mappedBy = "user")
//  @OneToOne(mappedBy = "user", cascade = CascadeType.ALL)
//  @OneToOne(cascade = CascadeType.ALL)
  @JoinColumn(name = "userId", referencedColumnName="userId", insertable = false, updatable = false)
  @OneToOne(mappedBy = "user")
  private UserBillingInformationXref billingInformation;
  @NotNull
  private LocalDateTime effectiveDate;
  private LocalDateTime terminationDate;
  private byte[] profileImage;

  @OneToMany(cascade= CascadeType.ALL, fetch = FetchType.EAGER, orphanRemoval = true, mappedBy = "id.userId")
  private Set<UserRolesXref> roles;

  @OneToMany(fetch = FetchType.EAGER)
  @JoinColumn(name = "userId", referencedColumnName = "userId", insertable = false, updatable = false)
  private Set<UserNotificationsConfigXref> notificationConfig;

  @OneToOne
  @JoinColumn(name = "hierarchyId", referencedColumnName = "nodeId", insertable = false, updatable = false)
  private HierarchyNode hierarchyData;

  public void merge(User dto) {
    if (null != dto) {
      Assert.notNull(dto.getFirstName(), "Firstname is null!");
      Assert.notNull(dto.getLastName(), "LastName is null!");
      Assert.notNull(dto.getEffectiveDate(), "Effective date is null!");

      this.firstName = checkForDirty(this.firstName, dto.getFirstName());
      this.lastName = checkForDirty(this.lastName, dto.getLastName());

      if (this.roles.size() != dto.getRoles().size()) {
        setDirty(true);
      } else {
        for (UserRolesXref r : this.roles) {
          if (dto.getRoles().stream().noneMatch(d -> d.getRoleId().equals(r.getId().getRoleId()))) {
            setDirty(true);
            break;
          }
        }
      }

      this.roles.removeIf(r -> dto.getRoles().stream().noneMatch(d -> d.getRoleId().equals(r.getId().getRoleId())));

      for (RoleDTO d: dto.getRoles()) {
        if (this.roles.stream().noneMatch(r -> r.getId().getRoleId().equals(d.getRoleId()))) {
          this.roles.add(new UserRolesXref(new UserRolesXrefId(dto.getUserId(), d.getRoleId()), null));
        }
      }
      this.isActive = checkForDirty(this.isActive, dto.getIsActive());
      this.effectiveDate = checkForDirty(this.effectiveDate, dto.getEffectiveDate());
      this.email = checkForDirty(this.email, dto.getEmail());
      this.title = checkForDirty(this.title, dto.getTitle());
      this.hierarchyId = checkForDirty(this.hierarchyId, dto.getHierarchyId());
      this.terminationDate = checkForDirty(this.terminationDate, dto.getTerminationDate());

      this.contacts = new UserContactXref();
      this.contacts.setUserId(userId);
      this.contacts.merge(UserContactDTO.builder().phone(dto.getPhone()).mobile(dto.getMobile()).address(dto.getAddress()).build());

      if (billingInformation != null) {
        billingInformation.setUser(null);
      }
    }
  }

  @Override
  public String key() {
    return email;
  }
}
