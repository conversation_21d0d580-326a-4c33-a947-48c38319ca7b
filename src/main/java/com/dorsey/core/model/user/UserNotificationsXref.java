package com.dorsey.core.model.user;

import com.dorsey.core.model.AbstractModel;
import com.dorsey.core.service.AuditListener;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EntityListeners;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.IdClass;
import jakarta.persistence.SequenceGenerator;
import java.time.LocalDateTime;
import java.util.UUID;

@Builder
@AllArgsConstructor
@EntityListeners(AuditListener.class)
@Entity
@NoArgsConstructor
@Getter
@Setter
@IdClass( UserNotificationsXrefId.class )
public class UserNotificationsXref extends AbstractModel {
  @Id
  @Column(name = "user_id")
  private UUID userId;
  @Id
  @Column(name = "id", insertable = false)
  @SequenceGenerator(name="notification_id_seq",sequenceName="notification_id_seq", allocationSize=1)
  @GeneratedValue(strategy=GenerationType.SEQUENCE,generator="notification_id_seq")
  private Integer id;
  private String type;
  private String body;
  private String url;
  private LocalDateTime createdDate;


  @Override
  public String key() {
    return getUserId() + "|" + getId();
  }
}
