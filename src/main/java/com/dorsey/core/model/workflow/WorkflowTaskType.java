package com.dorsey.core.model.workflow;

import com.dorsey.core.dto.task.WorkflowTaskTypeDTO;
import com.dorsey.core.model.AbstractModel;
import com.dorsey.core.model.lov.json.TaskTypeData;
import com.dorsey.core.model.lov.json.TaskTypeLovData;
import com.dorsey.core.service.AuditListener;


import com.vladmihalcea.hibernate.type.array.StringArrayType;
import com.vladmihalcea.hibernate.type.json.JsonType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.annotations.Type;
import org.hibernate.type.SqlTypes;
import org.springframework.util.Assert;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EntityListeners;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OneToOne;

import java.util.List;
import java.util.Set;
import java.util.UUID;


@Builder(toBuilder=true)
@AllArgsConstructor
@EntityListeners(AuditListener.class)
@Entity
@NoArgsConstructor
@Getter
@Setter
public class WorkflowTaskType extends AbstractModel {
  @Id
//  @GeneratedValue(strategy = GenerationType.AUTO)
  private UUID taskId;
  //NOTE: In the future might be "One to Many"
  @OneToOne
  @JoinColumn(name = "workflowId", referencedColumnName = "id")
  private Workflow workflow;
  private String taskName;
  @JdbcTypeCode(SqlTypes.JSON)
  @Type(JsonType.class)
  @Column(columnDefinition = "json")
//  private Object fields;
  private List<TaskTypeData> fields;
  @Column(columnDefinition = "BOOLEAN DEFAULT false")
  @Builder.Default
  private Boolean hasEmail = false;
  @JdbcTypeCode(SqlTypes.ARRAY)
  @Type(StringArrayType.class)
  @Column(
      name = "email_to",
      columnDefinition = "text[]"
  )
  private String[] emailTo;
  private String emailSubject;
  private String emailBody;
  @Column(columnDefinition = "BOOLEAN DEFAULT false")
  @Builder.Default
  private Boolean triggerWorkflow = false;
  @OneToOne(cascade = CascadeType.ALL)
  @JoinColumn(name = "triggerWorkflowId", referencedColumnName = "id")
  private WorkflowType workflowToTrigger;
  @Column(columnDefinition = "BOOLEAN DEFAULT false")
  @Builder.Default
  private Boolean hasDirectory = false;
  private Integer serviceLevelAgreement;
  private Integer serviceLevelAgreementWarning;
  private String description;
  private Boolean completed;

  @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.EAGER, mappedBy = "id.taskId")
  private Set<RoleWorkflowTaskXref> assignedTo;

  public void merge(WorkflowTaskTypeDTO dto) {
    if (null != dto) {
      Assert.notNull(dto.getTaskName(), "Task name is null!");

      this.taskName = checkForDirty(this.getTaskName(), dto.getTaskName());
    }
  }

  @Override
  public String key() {
    return taskId.toString();
  }
}
