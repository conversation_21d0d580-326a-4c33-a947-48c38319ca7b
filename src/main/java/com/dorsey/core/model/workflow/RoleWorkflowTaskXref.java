package com.dorsey.core.model.workflow;

import com.dorsey.core.model.AbstractModel;
import com.dorsey.core.model.roles.Role;
import com.dorsey.core.service.AuditListener;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import jakarta.persistence.Entity;
import jakarta.persistence.EntityListeners;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToOne;

@Builder(toBuilder=true)
@AllArgsConstructor
@EntityListeners(AuditListener.class)
@Entity
@NoArgsConstructor
@Getter
@Setter
public class RoleWorkflowTaskXref extends AbstractModel {
  @Id
  private RoleWorkflowTaskXrefId id;

  @OneToOne
  @JoinColumn(name = "roleId", referencedColumnName = "roleId", insertable = false, updatable = false)
  private Role role;

  @OneToOne
  @JoinColumn(name = "taskId", referencedColumnName = "taskId", insertable = false, updatable = false)
  @JsonIgnore
  private WorkflowTaskType workflowTaskInfo ;

  @Override
  public String key()  {
    return id.getRoleId() + "|" + id.getTaskId();
  }
}
