package com.dorsey.core.controller.item;

import com.dorsey.core.controller.AbstractController;
import com.dorsey.core.repository.item.ItemRepo;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/items")
@AllArgsConstructor
public class ItemController extends AbstractController {
  private ItemRepo itemRepo;

  @GetMapping("/checkTransitions")
  public Boolean checkTransitionsExistence() {
    return itemRepo.checkIfTransitionIdExist();
  }
}
