package com.dorsey.core.controller;

import lombok.AllArgsConstructor;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Controller;

@Controller
@AllArgsConstructor
public class WebSocketController {

  private final SimpMessagingTemplate template;

  public void addNotificationCount() {
    this.template.convertAndSend("/notification", "");
  }

  //TODO: Filter destinies.
//  @SendToUser("")
//  public String notify(String message) {
//    template.convertAndSendToUser(message.getToUser(), "/queue/reply", greeting);
//  }
}
