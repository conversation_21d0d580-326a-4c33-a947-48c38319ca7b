package com.dorsey.core.authorization;

import com.dorsey.core.dto.lov.Roles;
import com.dorsey.core.model.user.Users;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.security.Keys;
import lombok.AllArgsConstructor;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Component;

import jakarta.servlet.http.HttpServletResponse;
import java.time.Instant;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Component
@AllArgsConstructor
public class JwtTokenUtil {
  private ModelMapper modelMapper;

  public static void writeHeader(String token, HttpServletResponse response) {
    response.addHeader("Authorization", "Bearer " + token);
  }

  //generate token for user
  public String generateToken(Users userDetails) {
    Map<String, Object> claims = new HashMap<>();
    claims.put("role", modelMapper.map(userDetails.getRoles(), Roles.class));
    claims.put("firstName", userDetails.getFirstName());
    claims.put("lastName", userDetails.getLastName());
    claims.put("title", userDetails.getTitle());
    return generateToken(userDetails, claims);
  }

  public String generateToken(Users userDetails, Map<String, Object> claims) {
    String ret = null;
    if (null != userDetails) {
      ret = doGenerateToken(claims, userDetails.getEmail());
    }

    return ret;
  }

  //while creating the token -
//1. Define  claims of the token, like Issuer, Expiration, Subject, and the ID
//2. Sign the JWT using the HS512 algorithm and secret key.
//3. According to JWS Compact Serialization(https://tools.ietf.org/html/draft-ietf-jose-json-web-signature-41#section-3.1)
//   compaction of the JWT to a URL-safe string
  private String doGenerateToken(Map<String, Object> claims, String subject) {
    Instant now = Instant.now();
    return Jwts.builder().setHeaderParam("kid", "0001").setHeaderParam("typ", "JWT").setClaims(claims).setSubject(subject).setIssuedAt(Date.from(now))
        .setExpiration(Date.from(now.plusSeconds(-1)))
        .signWith(Keys.secretKeyFor(SignatureAlgorithm.HS256)).compact();
  }
}
