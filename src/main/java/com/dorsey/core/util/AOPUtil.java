package com.dorsey.core.util;

import org.aspectj.lang.ProceedingJoinPoint;

import java.lang.reflect.Method;

public final class AOPUtil {
  private AOPUtil() {
  }

  public static Method getCalledMethod(Class<?> clazz, ProceedingJoinPoint joinPoint) {
    Method ret = null;
    String calledMethodName = joinPoint.getSignature().getName();

    for (Method method : clazz.getMethods()) {
      if (calledMethodName.equals(method.getName())) {
        ret = method;
        break;
      }
    }

    return ret;
  }
}
