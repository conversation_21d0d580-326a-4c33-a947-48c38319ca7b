package com.dorsey.core.util;

import java.util.Map;

public final class MapUtils {
  private MapUtils() {
  }

  public static boolean isEmpty(final Map<?, ?> mapToCheck) {
    return mapToCheck == null || mapToCheck.isEmpty();
  }

  public static boolean isNotEmpty(final Map<?, ?> mapToCheck) {
    return !isEmpty(mapToCheck);
  }

  public static String convertParmMap(final Map<String, String> parmMap) {
    String ret = null;

    if (null != parmMap) {
      StringBuilder str = new StringBuilder();

      for (Map.Entry<String, String> entry : parmMap.entrySet()) {
        str.append(entry.getKey()).append(":").append(entry.getValue()).append(" | ");
      }

      if (str.length() > 3) {
        ret = str.substring(0, str.length() - 3);
      }
    }
    return ret;
  }
}
