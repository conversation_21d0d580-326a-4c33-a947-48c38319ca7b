package com.dorsey.core.util;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import jakarta.validation.constraints.NotNull;

@Component
public class SpringContext implements ApplicationContextAware {
  private static ApplicationContext applicationContext;

  public static ApplicationContext getAppContext() {
    return applicationContext;
  }

  public static <T> T getBean(Class<T> clazz) {
    return applicationContext.getBean(clazz);
  }

  public static Object getBean(String className) {
    return applicationContext.getBean(className);
  }

  public static String getValue(String name) {
    return applicationContext.getEnvironment().getProperty(name);
  }

  @Override
  public void setApplicationContext(@NotNull ApplicationContext applicationContext) throws BeansException {
    SpringContext.applicationContext = applicationContext;
  }
}
