package com.dorsey.core.service.role;

import com.dorsey.core.dto.roles.RoleDTO;
import com.dorsey.core.enums.CacheName;
import com.dorsey.core.exception.NotFoundException;
import com.dorsey.core.mapper.role.RoleMapper;
import com.dorsey.core.model.roles.Role;
import com.dorsey.core.model.roles.RoleCapabilitiesXref;
import com.dorsey.core.model.roles.RoleCapabilitiesXrefId;
import com.dorsey.core.model.workflow.RoleWorkflowTaskXref;
import com.dorsey.core.model.workflow.RoleWorkflowTaskXrefId;
import com.dorsey.core.repository.role.RoleRepo;
import com.dorsey.core.service.AbstractService;
import io.jsonwebtoken.lang.Assert;
import jakarta.inject.Inject;
import lombok.AllArgsConstructor;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.stereotype.Service;


import java.util.ArrayList;
import java.util.List;
import java.util.UUID;


@Service
@AllArgsConstructor
public class RoleService extends AbstractService {

  private RoleRepo roleRepository;
  @Inject
  private RoleMapper roleMapper;

  public RoleDTO retrieveRole(UUID roleId) {
    return modelMapper.map(roleRepository.findById(roleId).orElse(null), RoleDTO.class);
  }

  @SuppressWarnings("unchecked")
  public List<RoleDTO> retrieveAllRoles() {
    modelMapper.typeMap(Role.class, RoleDTO.class).addMapping(Role::getCapabilities, (dest, value) -> dest.capabilitiesFromModel((List<RoleCapabilitiesXref>) value));
    modelMapper.typeMap(Role.class, RoleDTO.class).addMapping(Role::getTasks, (dest, value) -> dest.tasksFromModel((List<RoleWorkflowTaskXref>) value));
    return roleRepository.findAllExceptSystemRoles().stream().map(entity -> modelMapper.map(entity, RoleDTO.class)).toList();
  }

  public List<RoleDTO> retrieveRolesByName(List<String> names) {
    return roleRepository.findByNameIsIn(names).stream().map(entity -> roleMapper.roleDTO(entity)).toList();
  }

  @CacheEvict(value = CacheName.USERS, allEntries = true)
  public List<RoleDTO> updateRole(List<RoleDTO> dtos) {
    Assert.notNull(dtos, "Roles are null!");

    var entities = roleRepository.findAll();
    var deletedIds = entities.stream().map(Role::getRoleId).filter(id -> !dtos.stream().map(RoleDTO::getRoleId).toList().contains(id)).toList();
    var addIds = entities.stream().map(Role::getRoleId).toList();
    var newEntities = dtos.stream().filter(d -> !addIds.contains(d.getRoleId())).toList();
    List<Role> updatedEntities = new ArrayList<>();

    for (Role entity : entities) {
      dtos.stream().filter(d -> entity.getRoleId().equals(d.getRoleId())).findFirst().ifPresent(entity::merge);
      if (entity.isDirty()) {
        updatedEntities.add(entity);
      }
    }

    for (RoleDTO dto : newEntities) {
      List<RoleCapabilitiesXref> capabilities = new ArrayList<>();
      var roleId = UUID.randomUUID();

      dto.getCapabilities().forEach(c -> {
        capabilities.add(
            RoleCapabilitiesXref.builder()
                .id(new RoleCapabilitiesXrefId(roleId, String.join(",", c.getComponent())))
                .view(c.getView())
                .edit(c.getEdit())
                .create(c.getCreate())
                .delete(c.getDelete())
                .build()
        );
      });

      updatedEntities.add(
          Role.builder()
              .roleId(roleId)
              .name(dto.getName())
              .department(dto.getDepartment())
              .description(dto.getDescription())
              .capabilities(capabilities)
              .build());
    }

    roleRepository.saveAll(updatedEntities);
    roleRepository.deleteAllById(deletedIds);


    return retrieveAllRoles();
  }

  @CacheEvict(value = CacheName.USERS, allEntries = true)
  public RoleDTO updateCapabilities(RoleDTO dto) {
    Assert.notNull(dto, "Role cannot be null");
    var dbEntity = roleRepository.findById(dto.getRoleId()).orElseThrow(() -> new NotFoundException("Role not found."));

    List<RoleCapabilitiesXref> capabilities = new ArrayList<>();
    List<RoleWorkflowTaskXref> tasks = new ArrayList<>();

    dto.getCapabilities().forEach(c -> {
      capabilities.add(
          RoleCapabilitiesXref.builder()
              .id(new RoleCapabilitiesXrefId(dto.getRoleId(), String.join(",", c.getComponent())))
              .view(c.getView())
              .edit(c.getEdit())
              .create(c.getCreate())
              .delete(c.getDelete())
              .build()
      );
    });

    dto.getTasks().forEach(c -> {
      tasks.add(
          RoleWorkflowTaskXref.builder()
              .id(new RoleWorkflowTaskXrefId(dto.getRoleId(), c.getTaskId()))
              .build()
      );
    });

    var entity = Role.builder()
        .roleId(dbEntity.getRoleId())
        .name(dto.getName())
        .department(dto.getDepartment())
        .capabilities(capabilities)
        .tasks(tasks)
        .superiorId(dto.getSuperiorId())
        .description(dto.getDescription())
        .build();

    return modelMapper.map(roleRepository.save(entity), RoleDTO.class);
  }

  public Boolean checkRoleNamesExistence(List<String> names) {
    Assert.notNull(names, "Role names cannot be null");

    return roleRepository.checkIfNamesExist(names, names.size());
  }
}
