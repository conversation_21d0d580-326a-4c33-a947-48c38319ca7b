package com.dorsey.core.service.dataload.processors;

import com.dorsey.core.exception.ServiceException;
import com.dorsey.core.model.organization.HierarchyLevel;
import com.dorsey.core.model.organization.HierarchyNode;
import com.dorsey.core.repository.organization.HierarchyLevelRepo;
import com.dorsey.core.repository.organization.HierarchyNodeRepo;
import com.dorsey.core.service.dataload.processors.headers.HierarchyLevelHeader;
import com.dorsey.core.service.dataload.processors.headers.HierarchyTreeHeader;
import com.dorsey.core.service.dataload.processors.rows.HierarchyLevelRow;
import com.dorsey.core.service.dataload.processors.rows.HierarchyTreeRow;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.jxls.reader.ReaderBuilder;
import org.springframework.stereotype.Component;
import org.xml.sax.SAXException;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Component
@AllArgsConstructor
public class OHProcessor {
  private static final String FILE_NAME = "oh.xml";
  private HierarchyLevelRepo hierarchyLevelRepo;
  private HierarchyNodeRepo hierarchyNodeRepo;

  public void process(InputStream inputXLS) throws IOException, SAXException, InvalidFormatException {
    List<HierarchyLevelRow> hierarchyLevelRows = new ArrayList<>();
    List<HierarchyTreeRow> hierarchyTreeRows = new ArrayList<>();

    this.parse(inputXLS, hierarchyLevelRows, hierarchyTreeRows);

    List<HierarchyLevel> dbHierarchyLevels = new ArrayList<>();

    for (HierarchyLevelRow row : hierarchyLevelRows) {
      var data = HierarchyLevel.builder()
          .level(NumberUtils.toInt(row.getLevel()))
          .hierarchyName(row.getHierarchyName())
          .description(row.getDescription())
          .build();
      dbHierarchyLevels.add(data);
    }

    dbHierarchyLevels.sort(Comparator.comparing(HierarchyLevel::getLevel));
    HashMap<String, Integer> nodes = new HashMap<>();

    List<HierarchyNode> dbHierarchyNodes = new ArrayList<>();
    hierarchyTreeRows.sort(Comparator.comparing(HierarchyTreeRow::getLevel));
    var counter = 0;

    for (HierarchyTreeRow row : hierarchyTreeRows) {
      ++counter;
      nodes.put(row.getHierarchyValue(), counter);

      var data = HierarchyNode.builder()
          .nodeId(counter)
          .parentId(row.getParent() != null ? nodes.get(row.getParent()): 0)
          .level(NumberUtils.toInt(row.getLevel()))
          .hierarchyValue(row.getHierarchyValue())
          .build();
      dbHierarchyNodes.add(data);
    }

    hierarchyNodeRepo.deleteAllInBatch();
    hierarchyLevelRepo.deleteAllInBatch();

    hierarchyLevelRepo.saveAll(dbHierarchyLevels);
    hierarchyNodeRepo.saveAll(dbHierarchyNodes);
  }

  public void parse(InputStream inputXLS, List<HierarchyLevelRow> hierarchyLevelRows, List<HierarchyTreeRow> hierarchyTreeRows) throws IOException, SAXException, InvalidFormatException {
    var levelHeader = new HierarchyLevelHeader();
    var treeHeader = new HierarchyTreeHeader();
    List<String> results = new ArrayList<>();

    try (inputXLS; InputStream config = getClass().getResourceAsStream("/templates/mapper/" + FILE_NAME)) {

      Workbook workbook = new XSSFWorkbook(inputXLS);
      results = validateSheets(workbook);
      if (!results.isEmpty()) {
        throw new ServiceException(String.join(";", results));
      }

      ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
      workbook.write(outputStream);
      InputStream input = new ByteArrayInputStream(outputStream.toByteArray());

      var mainReader = ReaderBuilder.buildFromXML(config);
      Map<String, Object> beans = new HashMap<>();
      beans.put("levelHeader", levelHeader);
      beans.put("levelRows", hierarchyLevelRows);
      beans.put("treeHeader", treeHeader);
      beans.put("treeRows", hierarchyTreeRows);
      mainReader.read(input, beans);
    }

    results = validateLevel(levelHeader, hierarchyLevelRows);
    if (!results.isEmpty()) {
      throw new ServiceException(String.join(";", results));
    }

    results = validateTree(treeHeader, hierarchyTreeRows, hierarchyLevelRows.size());
    if (!results.isEmpty()) {
      throw new ServiceException(String.join(";", results));
    }
  }

  private List<String> validateSheets(Workbook workbook) {
    List<String> errors = new ArrayList<>();

    if (workbook.getSheet("Hierarchy Levels") == null) {
      errors.add("Spreadsheet <b>'Hierarchy Levels'</b> not found in this Workbook.");
    }
    if (workbook.getSheet("Hierarchy Tree") == null) {
      errors.add("Spreadsheet <b>'Hierarchy Tree'</b> not found in this Workbook.");
    }
    return errors;
  }

  private List<String> validateLevel(HierarchyLevelHeader header, List<HierarchyLevelRow> rows) {
    List<String> errors = new ArrayList<>();

    errors.addAll(validateLevelHeader(header));
    errors.addAll(validateLevelRows(rows));
    return errors;
  }
  private List<String> validateLevelHeader(HierarchyLevelHeader header) {
    List<String> errors = new ArrayList<>();
    if (header.getLevel() == null || !header.getLevel().trim().equals("Level")) {
      errors.add("<b>Hierarchy Levels</b>: Header <b>'Level'</b> is missing in column A.");
    }
    if (header.getHierarchyName() == null || !header.getHierarchyName().trim().equals("Hierarchy name")) {
      errors.add("<b>Hierarchy Levels</b>: Header <b>'Hierarchy name'</b> is missing in column B.");
    }
    if (header.getDescription() == null || !header.getDescription().trim().equals("Description")) {
      errors.add("<b>Hierarchy Levels</b>: Header <b>'Description'</b> is missing in column C.");
    }
    return errors;
  }

  private List<String> validateLevelRows(List<HierarchyLevelRow> rows) {
    List<String> errors = new ArrayList<>();
    var checkGap = checkLevelGap(rows);

    for (int i = 0; i < rows.size(); i++) {
      try {
        var row = rows.get(i);

        if (StringUtils.isEmpty(row.getLevel())) {
          errors.add(MessageFormat.format("<b>Hierarchy Levels - Row #{0}</b>: Level cannot be null.", i + 2));
        } else if (!StringUtils.isNumeric(row.getLevel())) {
          errors.add(MessageFormat.format("<b>Hierarchy Levels - Row #{0}</b>: Level should be numeric.", i + 2));
        } else if (NumberUtils.toInt(row.getLevel()) == 0) {
          errors.add(MessageFormat.format("<b>Hierarchy Levels - Row #{0}</b>: Level 0 is reserved for Organization. Levels should bea number from 1 to 5.", i + 2));
        }

        if (StringUtils.isEmpty(row.getHierarchyName())) {
          errors.add(MessageFormat.format("<b>Hierarchy Levels - Row #{0}</b>: Hierarchy name cannot be null.", i + 2));
        } else {
          if (row.getHierarchyName().trim().equals("organization")) {
            errors.add(MessageFormat.format("<b>Hierarchy Levels - Row #{0}</b>: Organization name is reserved for the organization.", i + 2));
          }
          if (row.getHierarchyName().length() > 25) {
            errors.add(MessageFormat.format("<b>Hierarchy Levels - Row #{0}</b>: Hierarchy name cannot contain more than 25 characters.", i + 2));
          }
        }


        if (StringUtils.isEmpty(row.getDescription())) {
          errors.add(MessageFormat.format("<b>Hierarchy Levels - Row #{0}</b>: Description cannot be null.", i + 2));
        } else if (row.getDescription().length() > 255) {
          errors.add(MessageFormat.format("<b>Hierarchy Levels - Row #{0}</b>: Description cannot contain more than 255 characters.", i + 2));
        }

      } catch (NumberFormatException e) {
        errors.add(MessageFormat.format("Row #{0}: A value length is over the max supported by the column data type.", i + 2));
      }
    }

    if (errors.isEmpty()) {
      if (checkGap != null) {
        errors.add(checkGap);
      }

      if (rows.size() > 5) {
        errors.add("<b>Hierarchy Levels</b>: Amount of levels passed the limit of 5 levels.");
      }
    }

    return errors;
  }

  private List<String> validateTree(HierarchyTreeHeader header, List<HierarchyTreeRow> rows, int levelAmount) {
    List<String> errors = new ArrayList<>();

    errors.addAll(validateTreeHeader(header));
    errors.addAll(validateTreeRows(rows, levelAmount));
    return errors;
  }

  private List<String> validateTreeHeader(HierarchyTreeHeader header) {
    List<String> errors = new ArrayList<>();
    if (header.getLevel() == null || !header.getLevel().trim().equals("Level")) {
      errors.add("<b>Hierarchy Tree</b>: Header <b>'Level'</b> is missing in column A.");
    }
    if (header.getHierarchyValue() == null || !header.getHierarchyValue().trim().equals("Hierarchy value")) {
      errors.add("<b>Hierarchy Tree</b>: Header <b>'Hierarchy value'</b> is missing in column B.");
    }
    if (header.getParent() == null || !header.getParent().trim().equals("Parent")) {
      errors.add("<b>Hierarchy Tree</b>: Header <b>'Parent'</b> is missing in column C.");
    }

    return errors;
  }

  private List<String> validateTreeRows(List<HierarchyTreeRow> rows, int levelAmount) {
    List<String> errors = new ArrayList<>();
    var hierarchyValues = rows.stream().map(HierarchyTreeRow::getHierarchyValue).filter(Objects::nonNull).toList();

    for (int i = 0; i < rows.size(); i++) {
      try {
        var row = rows.get(i);

        if (StringUtils.isEmpty(row.getLevel())) {
          errors.add(MessageFormat.format("<b>Hierarchy Tree - Row #{0}</b>: Level cannot be null.", i + 2));
        } else if (!StringUtils.isNumeric(row.getLevel())) {
          errors.add(MessageFormat.format("<b>Hierarchy Tree - Row #{0}</b>: Level should be numeric.", i + 2));
        } else {
          if (Integer.parseInt(row.getLevel()) > levelAmount) {
            errors.add(MessageFormat.format("<b>Hierarchy Tree - Row #{0}</b>: Level <b>{1}</b> doesn''t exist in <b>\"Hierarchy Levels\"</b> spreadsheet.", i + 2, row.getLevel()));
          }
          if (Integer.parseInt(row.getLevel()) > 1 && row.getParent() == null) {
            errors.add(MessageFormat.format("<b>Hierarchy Tree - Row #{0}</b>: A Hierarchy value with level above 1 should has a parent.", i + 2));
          }
        }

        if (StringUtils.isEmpty(row.getHierarchyValue())) {
          errors.add(MessageFormat.format("Row #{0}: Hierarchy value cannot be null.", i + 2));
        } else {
          if (row.getHierarchyValue().length() > 25) {
            errors.add(MessageFormat.format("<b>Hierarchy Tree - Row #{0}</b>: Hierarchy value cannot contain more than 25 characters.", i + 2));
          }
          if (row.getParent() != null && row.getParent().equals(row.getHierarchyValue())) {
            errors.add(MessageFormat.format("<b>Hierarchy Tree - Row #{0}</b>: A Hierarchy value cannot has itself as Parent.", i + 2));
          }
          if (hierarchyValues.stream().filter(p -> p.equals(row.getHierarchyValue())).count() > 1) {
            errors.add(MessageFormat.format("<b>Hierarchy Tree - Row #{0}</b>: Hierarchy value is duplicated.", i + 2));
          } else {
            if (row.getParent() != null) {
              var parentRow = rows.stream().filter(v -> v.getHierarchyValue().equals(row.getParent())).findFirst().orElse(null);
              if (parentRow != null && !StringUtils.isEmpty(parentRow.getLevel()) && !StringUtils.isEmpty(row.getLevel()) && StringUtils.isNumeric(parentRow.getLevel()) && StringUtils.isNumeric(row.getLevel()) && (Integer.parseInt(row.getLevel()) - 1) != Integer.parseInt(parentRow.getLevel()) ) {
                errors.add(MessageFormat.format("<b>Hierarchy Tree - Row #{0}</b>: Parent level (<b>{1}</b>) should be one digit below to its level (<b>{2}</b>)", i + 2, parentRow.getLevel(), row.getLevel()));
              }
            }
          }
        }
//        if (row.getParent() == null) {
//          errors.add(MessageFormat.format("<b>Hierarchy Tree - Row #{0}</b>: Parent value cannot be empty.", i + 2));
//        } else if (hierarchyValues.stream().noneMatch(p -> row.getParent().equalsIgnoreCase("organization") || p.equals(row.getParent()))) {
//          errors.add(MessageFormat.format("<b>Hierarchy Tree - Row #{0}</b>: Parent doesn''t exist in <b>\"Hierarchy value\"</b> column.", i + 2));
//        }
        if (row.getParent() != null && hierarchyValues.stream().noneMatch(p -> p.equals(row.getParent()))) {
          errors.add(MessageFormat.format("<b>Hierarchy Tree - Row #{0}</b>: Parent doesn''t exist in <b>\"Hierarchy value\"</b> column.", i + 2));
        }
      } catch (NumberFormatException e) {
        errors.add(MessageFormat.format("<b>Hierarchy Tree - Row #{0}</b>: Row #{0}: A value length is over the max supported by the column data type.", i + 2));
      }
    }

    if (errors.isEmpty()) {
      if (rows.stream().map(HierarchyTreeRow::getLevel).map(Integer::valueOf).anyMatch(v -> v > levelAmount)) {
        errors.add(MessageFormat.format("<b>Hierarchy Tree</b>: Amount of levels (<b>{0}</b>) passed the limit of 5 levels.", levelAmount));
      }
    }

    return errors;
  }

  private String checkLevelGap(List<HierarchyLevelRow> rows) {
    var levels = rows.stream().map(HierarchyLevelRow::getLevel).map(Integer::valueOf).toList();
    int total = 0;

    for (int i = 0; i < levels.size(); i++) {
      total = i + 1;
      if (levels.get(i) > total) {
        return "<b>Hierarchy Levels</b>: Level column shouldn't contain gaps, <b>e.g.:</b> 1, 2, 4, 5 (level 3 is missing)";
      }
    }

    return null;
  }

}


