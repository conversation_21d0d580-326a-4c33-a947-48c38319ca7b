package com.dorsey.core.service.dataload.processors;

import com.dorsey.core.exception.ServiceException;
import com.dorsey.core.model.roles.Role;
import com.dorsey.core.repository.role.RoleRepo;
import com.dorsey.core.service.dataload.processors.headers.RoleHeader;
import com.dorsey.core.service.dataload.processors.rows.RoleRow;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;

import org.jxls.reader.ReaderBuilder;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.xml.sax.SAXException;

import java.io.IOException;
import java.io.InputStream;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Collectors;

@Component
@AllArgsConstructor
public class RProcessor {
  private static final String FILE_NAME = "roles.xml";

  private RoleRepo roleRepo;

  @Transactional
  public void process(InputStream inputXLS) throws IOException, SAXException, InvalidFormatException {
    var dbRows = roleRepo.findAll();
    var rows = this.parse(inputXLS);

    for (RoleRow row : rows) {
      var data = Role.builder()
          .roleId(UUID.randomUUID())
          .name(row.getRoleName())
          .department(row.getDepartment())
          .description(row.getDescription())
          .build();

      if (dbRows.stream().anyMatch(dbr -> row.getRoleName().equals(dbr.getName()))) {
        roleRepo.updateRoles(data.getName(), data.getDepartment(), data.getDescription());
      } else {
        roleRepo.save(data);
      }
    }
  }

  public List<RoleRow> parse(InputStream inputXLS) throws IOException, SAXException, InvalidFormatException {
    List<RoleRow> rows = new ArrayList<>();
    var header = new RoleHeader();

    try (inputXLS; InputStream config = getClass().getResourceAsStream("/templates/mapper/" + FILE_NAME)) {
      var mainReader = ReaderBuilder.buildFromXML(config);
      Map<String, Object> beans = new HashMap<>();
      beans.put("header", header);
      beans.put("rows", rows);
      mainReader.read(inputXLS, beans);
    }

    List<String> results = validate(header, rows);
    if (!results.isEmpty()) {
      throw new ServiceException(String.join(";", results));
    }
    return rows;
  }

  private List<String> validate(RoleHeader header, List<RoleRow> rows) {
    List<String> errors = new ArrayList<>();

    errors.addAll(validateHeader(header));
    errors.addAll(validateRows(rows));
    return errors;
  }

  private List<String> validateHeader(RoleHeader header) {
    List<String> errors = new ArrayList<>();
    if (header.getRoleName() == null || !header.getRoleName().trim().equals("Role Name")) {
      errors.add("Header <b>'Role Name'</b> is missing in column A.");
    }
    if (header.getDepartment() == null || !header.getDepartment().trim().equals("Department")) {
      errors.add("Header <b>'Department'</b> is missing in column B.");
    }
    if (header.getDescription() == null || !header.getDescription().trim().equals("Description")) {
      errors.add("Header <b>'Description'</b> is missing in column C.");
    }

    return errors;
  }

  private List<String> validateRows(List<RoleRow> rows) {
    List<String> errors = new ArrayList<>();

    var departments = rows.stream().map(RoleRow::getDepartment).collect(Collectors.toSet());

    if (departments.size() > 8) {
      errors.add("Number of Departments cannot be more than 8.");
    }

    for (int i = 0; i < rows.size(); i++) {
      try {
        var row = rows.get(i);

        if (StringUtils.isEmpty(row.getRoleName())) {
          errors.add(MessageFormat.format("<b>Row #{0}</b>: Role name cannot be null.", i + 2));
        } else {
          if (row.getRoleName().length() > 15) {
            errors.add(MessageFormat.format("<b>Row #{0}</b>: Role name cannot contain more than 15 characters.", i + 2));
          }
          if (rows.stream().map(RoleRow::getRoleName).filter(Objects::nonNull).filter(s -> s.equals(row.getRoleName())).count() > 1) {
            errors.add(MessageFormat.format("<b>Row #{0}</b>: Role name is duplicated.", i + 2));
          }
        }
        if (StringUtils.isEmpty(row.getDepartment())) {
          errors.add(MessageFormat.format("<b>Row #{0}</b>: Department cannot be null.", i + 2));
        } else if (row.getDepartment().length() > 15) {
          errors.add(MessageFormat.format("<b>Row #{0}</b>: Department cannot contain more than 15 characters.", i + 2));
        }

        if (StringUtils.isEmpty(row.getDescription())) {
          errors.add(MessageFormat.format("<b>Row #{0}</b>: Description cannot be null.", i + 2));
        } else if (row.getDescription().length() > 2047) {
          errors.add(MessageFormat.format("<b>Row #{0}</b>: Description cannot contain more than 2047 characters.", i + 2));
        }

      } catch (NumberFormatException e) {
        errors.add(MessageFormat.format("<b>Row #{0}</b>: A value length is over the max supported by the column data type.", i + 2));
      }
    }

    return errors;
  }
}


