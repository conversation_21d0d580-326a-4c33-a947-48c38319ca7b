package com.dorsey.core.service.dataload;

import com.dorsey.core.enums.DataSet;
import com.dorsey.core.exception.ServiceException;
import com.dorsey.core.model.lov.DataSetLov;
import com.dorsey.core.service.dataload.processors.LogoProcessor;
import com.dorsey.core.service.dataload.processors.OHProcessor;
import com.dorsey.core.service.dataload.processors.RProcessor;
import com.dorsey.core.service.dataload.processors.UProcessor;
import lombok.AllArgsConstructor;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.xml.sax.SAXException;

import java.io.IOException;
import java.io.InputStream;


@Service
@AllArgsConstructor
public class CoreFileProcessorService implements FileProcessor {
  private LogoProcessor logoProcessor;
  private RProcessor rProcessor;
  private OHProcessor ohProcessor;
  private UProcessor uProcessor;

  public void process(DataSetLov dataSetLov, MultipartFile file) {
    DataSet dataSet = DataSet.fromLov(dataSetLov);
    if (dataSet == null) {
      return;
    }
    try (InputStream inputStream = file.getInputStream()) {
      switch (dataSet) {
        case LOGO -> logoProcessor.process(inputStream);
        case R -> rProcessor.process(inputStream);
        case OH -> ohProcessor.process(inputStream);
        case U -> uProcessor.process(inputStream);
      }
    } catch (IOException | InvalidFormatException | SAXException e) {
      throw new ServiceException(e.getMessage(), e);
    }
  }
}
