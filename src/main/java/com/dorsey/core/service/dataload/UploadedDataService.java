package com.dorsey.core.service.dataload;

import com.dorsey.core.dto.dataload.UploadedDataDTO;
import com.dorsey.core.enums.AuditAction;
import com.dorsey.core.exception.NotFoundException;
import com.dorsey.core.model.dataload.UploadedData;
import com.dorsey.core.model.logging.AuditLog;
import com.dorsey.core.model.lov.DataSetLov;
import com.dorsey.core.model.lov.UploadedDataStatusLov;
import com.dorsey.core.repository.audit.AuditLogRepo;
import com.dorsey.core.repository.dataload.UploadedDataRepo;
import com.dorsey.core.repository.dataload.UploadedDataStatusLovRepo;
import com.dorsey.core.repository.file.FileRepository;
import com.dorsey.core.repository.lov.DataSetLovRepo;
import com.dorsey.core.service.AbstractService;
import com.dorsey.core.util.UserUtil;
import lombok.AllArgsConstructor;
import org.modelmapper.internal.util.Assert;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.nio.file.Path;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Service
@AllArgsConstructor
public class UploadedDataService extends AbstractService {
  private UploadedDataRepo uploadedDataRepo;
  private UploadedDataStatusLovRepo uploadedDataStatusLovRepo;
  private DataSetLovRepo dataSetLovRepo;
  private FileRepository fileRepository;
  private CoreFileProcessorService fileProcessorService;
  private AuditLogRepo auditLogRepo;
  private UserUtil userUtil;

  public List<UploadedDataDTO> retrieveUploadedData() {
    var dtos = uploadedDataRepo.findAllExceptLogo().stream().map(entity -> modelMapper.map(entity, UploadedDataDTO.class)).toList();
    var dataSetLovs = dataSetLovRepo.findAll();
    var dataStatusLovs = uploadedDataStatusLovRepo.findAll();

    dtos.forEach( dto -> {
      dto.setDataSet(dataSetLovs.stream().filter(l -> l.getCode().equals(dto.getDataSetCode())).map(DataSetLov::getDescription).findFirst().orElse(null));
      dto.setDisplayOrder(dataSetLovs.stream().filter(l -> l.getCode().equals(dto.getDataSetCode())).map(DataSetLov::getDisplayOrder).findFirst().orElse(null));
    });
    dtos.forEach( dto -> dto.setStatus(dataStatusLovs.stream().filter(l -> l.getCode().equals(dto.getStatusCode())).map(UploadedDataStatusLov::getDescription).findFirst().orElse(null)));

    return dtos;
  }

  public Optional<File> getFile(String id) {

    Optional<File> result = Optional.empty();
    final Optional<UploadedData> uplodadedDataOpt = uploadedDataRepo.findById(id);

    if (uplodadedDataOpt.isPresent()) {
      var uplodadedData = uplodadedDataOpt.get();
      if (uplodadedData.getFileName() != null) {
        final Path path = fileRepository.createPath(uplodadedData.getDataSetCode(), uplodadedData.getFileName());
        result = Optional.of(path.toFile());
      }
    }
    return result;

  }

  public UploadedDataDTO updateUploadedData(FileProcessor fileProcessor ,UploadedDataDTO dto, MultipartFile file) {
    Assert.notNull(dto);
    Assert.notNull(file);
    Assert.notNull(dto.getDataSetCode());

    fileProcessor.process(dataSetLovRepo.findById(dto.getDataSetCode()).orElseThrow(() -> new NotFoundException("Dataset not found for code: " + dto.getDataSetCode())), file);

    dto.setFileName(fileRepository.saveFile(dto.getDataSetCode(), file));
    var uploadedDataStatus = uploadedDataStatusLovRepo.findByDescription(dto.getStatus()).orElseThrow(() -> new NotFoundException("Update status not found!"));

    UploadedData ret = new UploadedData(dto.getDataSetCode(), dto.getFileName(), uploadedDataStatus.getCode(), LocalDateTime.now());

    auditLogRepo.save(new AuditLog(AuditAction.UPLOAD, ret, userUtil.getCurrentUser()));
    uploadedDataRepo.save(ret);
    dto.setUploadedDate(ret.getUploadedDate());

    return dto;
  }
}
