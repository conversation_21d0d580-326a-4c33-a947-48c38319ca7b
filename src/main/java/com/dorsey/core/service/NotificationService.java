package com.dorsey.core.service;

import com.dorsey.core.controller.WebSocketController;
import com.dorsey.core.dto.users.UserNotificationConfigDTO;
import com.dorsey.core.dto.users.UserNotificationDTO;
import com.dorsey.core.model.user.UserNotificationsConfigXref;
import com.dorsey.core.model.user.UserNotificationsConfigXrefId;
import com.dorsey.core.model.user.UserNotificationsXref;
import com.dorsey.core.model.user.UserNotificationsXrefId;
import com.dorsey.core.repository.user.UserNotificationsConfigXrefRepo;
import com.dorsey.core.repository.user.UserNotificationsXrefRepo;
import com.dorsey.core.util.UserUtil;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.UUID;

@Service
@AllArgsConstructor
public class NotificationService extends AbstractService {
  private UserNotificationsXrefRepo userNotificationsXrefRepo;
  private UserNotificationsConfigXrefRepo userNotificationsConfigXrefRepo;
  private UserUtil userUtil;
  private final WebSocketController webSocketController;

  public List<UserNotificationDTO> retrieveUserNotifications() {
    return userNotificationsXrefRepo.findByUserId(userUtil.getCurrentUser().getUserId()).stream().map(entity -> modelMapper.map(entity, UserNotificationDTO.class)).toList();
  }

  public void createNotification(UserNotificationsXref notification) {
    userNotificationsXrefRepo.save(notification);
  }

  public void createNotification(List<UserNotificationsXref> notifications) {
    userNotificationsXrefRepo.saveAll(notifications);
    webSocketController.addNotificationCount();
  }

  public void updateUserNotificationConfig(UserNotificationConfigDTO type) {
    userNotificationsConfigXrefRepo.save(new UserNotificationsConfigXref(new UserNotificationsConfigXrefId(userUtil.getCurrentUser().getUserId(), type.getType()), type.getObjectId(), type.getNotify()));
  }

  public void deleteUserNotification(UUID userId, Integer id) {
    userNotificationsXrefRepo.deleteById(new UserNotificationsXrefId(userId, id));
    webSocketController.addNotificationCount();
  }
  public List<UserNotificationConfigDTO> retrieveUserNotificationConfig() {
    return userNotificationsConfigXrefRepo.findByIdUserId(userUtil.getCurrentUser().getUserId()).stream().map(entity -> modelMapper.map(entity, UserNotificationConfigDTO.class)).toList();
  }
}
