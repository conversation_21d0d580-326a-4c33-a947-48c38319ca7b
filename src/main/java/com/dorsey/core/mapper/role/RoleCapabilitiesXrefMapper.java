package com.dorsey.core.mapper.role;


import com.dorsey.core.dto.roles.RoleCapabilitiesDTO;
import com.dorsey.core.model.roles.RoleCapabilitiesXref;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.Arrays;
import java.util.List;

@Mapper(componentModel = "spring")
public interface RoleCapabilitiesXrefMapper {
  RoleCapabilitiesXrefMapper INSTANCE = Mappers.getMapper(RoleCapabilitiesXrefMapper.class );

  @Mapping(target = "roleId", source = "id.roleId")
  @Mapping(target = "component", source = "id.component")
  RoleCapabilitiesDTO roleDTO(RoleCapabilitiesXref entity);

  default List<String> mapStringToList(String component) {
    if (component == null || component.isEmpty()) {
      return List.of();
    }
    return Arrays.asList(component.split(","));
  }
}
