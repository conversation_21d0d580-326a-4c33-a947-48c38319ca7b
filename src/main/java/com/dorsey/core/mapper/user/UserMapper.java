package com.dorsey.core.mapper.user;

import com.dorsey.core.dto.users.User;
import com.dorsey.core.mapper.organization.HierarchyNodeMapper;
import com.dorsey.core.model.user.Users;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring", uses = HierarchyNodeMapper.class)
public interface UserMapper {
  UserMapper INSTANCE = Mappers.getMapper( UserMapper.class );

  User userMapper(Users entity);
}
