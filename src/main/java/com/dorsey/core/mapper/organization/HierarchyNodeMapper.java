package com.dorsey.core.mapper.organization;

import com.dorsey.core.dto.organization.HierarchyLevelDTO;
import com.dorsey.core.dto.organization.HierarchyNodeDTO;
import com.dorsey.core.model.organization.HierarchyLevel;
import com.dorsey.core.model.organization.HierarchyNode;
import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring")
public interface HierarchyNodeMapper {
  HierarchyNodeMapper INSTANCE = Mappers.getMapper( HierarchyNodeMapper.class );
  @BeanMapping(ignoreByDefault = true)
  @Mapping(target = "hierarchyValue", source = "hierarchyValue")
  @Mapping(target = "id", source = "nodeId")
  @Mapping(target = "levelData", qualifiedByName = "hierarchyLevelMapper")
  HierarchyNodeDTO hierarchyNodeDTO(HierarchyNode entity);

//  HierarchyNode hierarchyNodeEntity(HierarchyNodeDTO dto);

  @Named("hierarchyLevelMapper")
  default HierarchyLevelDTO hierarchyLevelMapper(HierarchyLevel source) {
    return HierarchyLevelMapper.INSTANCE.hierarchyLevelDTO(source);
  }
}
