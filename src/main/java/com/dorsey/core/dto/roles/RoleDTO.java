package com.dorsey.core.dto.roles;

import com.dorsey.core.dto.task.RoleWorkflowTaskXrefDTO;
import com.dorsey.core.model.roles.RoleCapabilitiesXref;
import com.dorsey.core.model.workflow.RoleWorkflowTaskXref;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@EqualsAndHashCode
public class RoleDTO {
  private UUID roleId;
  private String name;
  private String department;
  private List<RoleCapabilitiesDTO> capabilities;
  private List<RoleWorkflowTaskXrefDTO> tasks;
  private UUID superiorId;
  private RoleDTO superior;
  private String description;

  public void capabilitiesFromModel(List<RoleCapabilitiesXref> modelCapabilities)  {
    List<RoleCapabilitiesDTO> capabilitiesDTO = new ArrayList<>();

    modelCapabilities.forEach(c ->
        capabilitiesDTO.add(
            RoleCapabilitiesDTO.builder()
                .roleId(c.getId().getRoleId())
                .component(c.convertComponentToList())
                .view(c.getView())
                .edit(c.getEdit())
                .create(c.getCreate())
                .delete(c.getDelete())
                .build())
    );

    capabilities = capabilitiesDTO;
  }

  public void tasksFromModel(List<RoleWorkflowTaskXref> modelTasks)  {
    if (modelTasks != null) {
      List<RoleWorkflowTaskXrefDTO> tasksDTO = new ArrayList<>();

      modelTasks.forEach(c ->
          tasksDTO.add(
              RoleWorkflowTaskXrefDTO.builder()
                  .roleId(c.getId().getRoleId())
                  .taskId(c.getId().getTaskId())
                  .role(c.getRole())
                  .build())
      );

      tasks = tasksDTO;
    }
  }
}
