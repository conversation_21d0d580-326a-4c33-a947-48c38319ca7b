package com.dorsey.core.dto.lov;

import com.dorsey.core.model.lov.json.TaskTypeLovData;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

@Builder
@AllArgsConstructor
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class WorkflowTypeLovDTO extends BaseLovDTO {
  private String code;
  private List<TaskTypeLovData> data;
}
