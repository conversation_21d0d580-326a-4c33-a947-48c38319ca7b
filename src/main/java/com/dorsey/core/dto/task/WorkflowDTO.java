package com.dorsey.core.dto.task;

import com.dorsey.core.model.workflow.WorkflowType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.UUID;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class WorkflowDTO {
  private UUID id;
  private WorkflowType workflowType;
  private Integer version;
  private Boolean active;
  private Boolean completed;
}
