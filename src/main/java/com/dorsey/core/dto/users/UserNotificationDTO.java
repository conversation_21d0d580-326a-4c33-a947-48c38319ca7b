package com.dorsey.core.dto.users;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.UUID;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class UserNotificationDTO {
  private UUID userId;
  private Integer id;
  private String type;
  private String body;
  private String url;
  private LocalDateTime createdDate;
}
