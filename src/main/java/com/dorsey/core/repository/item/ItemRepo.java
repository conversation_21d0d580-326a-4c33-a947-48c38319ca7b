package com.dorsey.core.repository.item;

import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class ItemRepo {
  @Value("${dorsey.itemEntity}")
  private String itemEntity;
  @Value("${dorsey.itemTransitionEntity}")
  private String itemTransitionEntity;

  private final JdbcTemplate jdbcTemplate;

  public Map<Integer, Long> getItemsAmountGroupedByHierarchyId() {
    return jdbcTemplate.queryForList( String.format("SELECT hierarchy_id id, count(hierarchy_id) amount FROM %s GROUP BY hierarchy_id", itemEntity) ).stream().collect(Collectors.toMap(k -> (Integer) k.get("id"), k -> (Long) k.get("amount")));
  }
  public Boolean checkIfTransitionIdExist() {
    return jdbcTemplate.queryForObject(String.format("SELECT CASE WHEN COUNT(*)>1 THEN true ELSE false END AS result FROM %s WHERE id>0", itemTransitionEntity), Boolean.class);
  }
}
