package com.dorsey.core.repository.dataload;

import com.dorsey.core.model.dataload.UploadedData;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;
import java.util.Optional;

public interface UploadedDataRepo extends JpaRepository<UploadedData, String> {

  @Query("SELECT a FROM UploadedData a WHERE a.dataSetCode <> 'LOGO'")
  List<UploadedData> findAllExceptLogo();
  Optional<UploadedData> findFirstByDataSetCodeAndStatusCodeNotIn(String dataSetCode, List<String> status);
}
