//package com.dorsey.core.repository.workflow;
//
//import com.dorsey.core.model.workflow.Workflow;
//import org.springframework.data.jpa.repository.JpaRepository;
//
//import java.util.List;
//import java.util.Optional;
//import java.util.UUID;
//
//public interface WorkflowRepo extends JpaRepository<Workflow, UUID> {
//
//  List<Workflow> findAllByCreatedBySystemIsFalse();
//
//  Optional<Workflow> findByWorkflow(String workflow);
//
//  Boolean existsByWorkflow(String workflow);
//
//  Boolean existsByWorkflowAndCompletedTrue(String workflow);
//}
